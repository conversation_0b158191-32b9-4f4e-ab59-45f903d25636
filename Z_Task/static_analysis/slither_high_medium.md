- [High] **arbitrary-send-erc20**: BorrowerOperations.closeTrove(uint256) (src/BorrowerOperations.sol#682-749) uses arbitrary from in transferFrom: WETH.transferFrom(gasPoolAddress,receiver,ETH_GAS_COMPENSATION) (src/BorrowerOperations.sol#741) 
- [High] **arbitrary-send-erc20**: TroveManager._sendGasCompensation(IActivePool,address,uint256,uint256) (src/TroveManager.sol#537-545) uses arbitrary from in transferFrom: WETH.transferFrom(gasPoolAddress,_liquidator,_eth) (src/TroveManager.sol#539) 
- [High] **arbitrary-send-eth**: WETHZapper._adjustTrovePost(uint256,bool,uint256,bool,address,LeftoversSweep.InitialBalances) (src/Zappers/WETHZapper.sol#199-226) sends eth to arbitrary user 	Dangerous calls: 	- (success,None) = _receiver.call{value: _collChange}() (src/Zappers/WETHZapper.sol#223) 
- [High] **unchecked-transfer**: ZapperAsFuck.closeTroveToRawETH(uint256) (src/Zappers/ZapperAsFuck.sol#225-243) ignores return value by boldToken.transferFrom(msg.sender,address(this),trove.entireDebt) (src/Zappers/ZapperAsFuck.sol#232) 
- [High] **unchecked-transfer**: TroveManager._sendGasCompensation(IActivePool,address,uint256,uint256) (src/TroveManager.sol#537-545) ignores return value by WETH.transferFrom(gasPoolAddress,_liquidator,_eth) (src/TroveManager.sol#539) 
- [High] **unchecked-transfer**: LeverageWETHZapper.receiveFlashLoanOnLeverDownTrove(ILeverageZapper.LeverDownTroveParams,uint256) (src/Zappers/LeverageWETHZapper.sol#171-195) ignores return value by WETH.transfer(address(flashLoanProvider),_params.flashLoanAmount) (src/Zappers/LeverageWETHZapper.sol#194) 
- [High] **unchecked-transfer**: LeverageWETHZapper.receiveFlashLoanOnOpenLeveragedTrove(ILeverageZapper.OpenLeveragedTroveParams,uint256) (src/Zappers/LeverageWETHZapper.sol#45-104) ignores return value by WETH.transfer(address(flashLoanProvider),_params.flashLoanAmount) (src/Zappers/LeverageWETHZapper.sol#102) 
- [High] **unchecked-transfer**: WETHZapper._adjustTrovePost(uint256,bool,uint256,bool,address,LeftoversSweep.InitialBalances) (src/Zappers/WETHZapper.sol#199-226) ignores return value by boldToken.transfer(_receiver,_boldChange) (src/Zappers/WETHZapper.sol#209) 
- [High] **unchecked-transfer**: GasCompZapper._adjustTrovePost(uint256,bool,uint256,bool,address,LeftoversSweep.InitialBalances) (src/Zappers/GasCompZapper.sol#205-225) ignores return value by boldToken.transfer(_receiver,_boldChange) (src/Zappers/GasCompZapper.sol#220) 
- [High] **unchecked-transfer**: ZapperAsFuck.repayBold(uint256,uint256) (src/Zappers/ZapperAsFuck.sol#123-138) ignores return value by boldToken.transferFrom(msg.sender,address(this),_boldAmount) (src/Zappers/ZapperAsFuck.sol#132) 
- [High] **unchecked-transfer**: ZapperAsFuck.withdrawBold(uint256,uint256,uint256) (src/Zappers/ZapperAsFuck.sol#112-121) ignores return value by boldToken.transfer(receiver,_boldAmount) (src/Zappers/ZapperAsFuck.sol#120) 
- [High] **unchecked-transfer**: GasCompZapper.openTroveWithRawETH(IZapper.OpenTroveParams) (src/Zappers/GasCompZapper.sol#29-90) ignores return value by boldToken.transfer(msg.sender,_params.boldAmount) (src/Zappers/GasCompZapper.sol#83) 
- [High] **unchecked-transfer**: CurveExchange.swapFromBold(uint256,uint256) (src/Zappers/Modules/Exchanges/CurveExchange.sol#34-47) ignores return value by boldToken.transferFrom(msg.sender,address(this),_boldAmount) (src/Zappers/Modules/Exchanges/CurveExchange.sol#37) 
- [High] **unchecked-transfer**: HybridCurveUniV3Exchange.swapFromBold(uint256,uint256) (src/Zappers/Modules/Exchanges/HybridCurveUniV3Exchange.sol#68-102) ignores return value by boldToken.transferFrom(msg.sender,address(this),_boldAmount) (src/Zappers/Modules/Exchanges/HybridCurveUniV3Exchange.sol#73) 
- [High] **unchecked-transfer**: ZapperAsFuck._adjustTrovePre(uint256,uint256,bool,uint256,bool,LeftoversSweep.InitialBalances) (src/Zappers/ZapperAsFuck.sol#176-201) ignores return value by boldToken.transferFrom(msg.sender,address(this),_boldChange) (src/Zappers/ZapperAsFuck.sol#197) 
- [High] **unchecked-transfer**: ZapperAsFuck.openTroveWithRawETH(IZapper.OpenTroveParams) (src/Zappers/ZapperAsFuck.sol#27-87) ignores return value by boldToken.transfer(msg.sender,_params.boldAmount) (src/Zappers/ZapperAsFuck.sol#80) 
- [High] **unchecked-transfer**: CurveExchange.swapToBold(uint256,uint256) (src/Zappers/Modules/Exchanges/CurveExchange.sol#49-64) ignores return value by boldToken.transfer(msg.sender,output) (src/Zappers/Modules/Exchanges/CurveExchange.sol#56) 
- [High] **unchecked-transfer**: GasCompZapper._adjustTrovePre(uint256,uint256,bool,uint256,bool,LeftoversSweep.InitialBalances) (src/Zappers/GasCompZapper.sol#179-203) ignores return value by boldToken.transferFrom(msg.sender,address(this),_boldChange) (src/Zappers/GasCompZapper.sol#199) 
- [High] **unchecked-transfer**: WETHZapper.receiveFlashLoanOnCloseTroveFromCollateral(IZapper.CloseTroveParams,uint256) (src/Zappers/WETHZapper.sol#274-302) ignores return value by WETH.transfer(address(flashLoanProvider),_params.flashLoanAmount) (src/Zappers/WETHZapper.sol#295) 
- [High] **unchecked-transfer**: ZapperAsFuck._adjustTrovePost(uint256,bool,uint256,bool,address,LeftoversSweep.InitialBalances) (src/Zappers/ZapperAsFuck.sol#203-223) ignores return value by boldToken.transfer(_receiver,_boldChange) (src/Zappers/ZapperAsFuck.sol#218) 
- [High] **unchecked-transfer**: WETHZapper.withdrawBold(uint256,uint256,uint256) (src/Zappers/WETHZapper.sol#102-111) ignores return value by boldToken.transfer(receiver,_boldAmount) (src/Zappers/WETHZapper.sol#110) 
- [High] **unchecked-transfer**: GasCompZapper.closeTroveToRawETH(uint256) (src/Zappers/GasCompZapper.sol#227-245) ignores return value by boldToken.transferFrom(msg.sender,address(this),trove.entireDebt) (src/Zappers/GasCompZapper.sol#234) 
- [High] **unchecked-transfer**: HybridCurveUniV3Exchange.swapToBold(uint256,uint256) (src/Zappers/Modules/Exchanges/HybridCurveUniV3Exchange.sol#105-143) ignores return value by boldToken.transfer(msg.sender,boldAmount) (src/Zappers/Modules/Exchanges/HybridCurveUniV3Exchange.sol#137) 
- [High] **unchecked-transfer**: WETHZapper._adjustTrovePost(uint256,bool,uint256,bool,address,LeftoversSweep.InitialBalances) (src/Zappers/WETHZapper.sol#199-226) ignores return value by boldToken.transfer(_initialBalances.receiver,currentBoldBalance - _initialBalances.balances[1]) (src/Zappers/WETHZapper.sol#215) 
- [High] **unchecked-transfer**: LeverageWETHZapper.receiveFlashLoanOnLeverUpTrove(ILeverageZapper.LeverUpTroveParams,uint256) (src/Zappers/LeverageWETHZapper.sol#125-150) ignores return value by WETH.transfer(address(flashLoanProvider),_params.flashLoanAmount) (src/Zappers/LeverageWETHZapper.sol#149) 
- [High] **unchecked-transfer**: WETHZapper._adjustTrovePre(uint256,uint256,bool,uint256,bool,LeftoversSweep.InitialBalances) (src/Zappers/WETHZapper.sol#166-197) ignores return value by boldToken.transferFrom(msg.sender,address(this),_boldChange) (src/Zappers/WETHZapper.sol#193) 
- [High] **unchecked-transfer**: GasCompZapper.withdrawBold(uint256,uint256,uint256) (src/Zappers/GasCompZapper.sol#115-124) ignores return value by boldToken.transfer(receiver,_boldAmount) (src/Zappers/GasCompZapper.sol#123) 
- [High] **unchecked-transfer**: CurveExchange.swapFromBold(uint256,uint256) (src/Zappers/Modules/Exchanges/CurveExchange.sol#34-47) ignores return value by boldToken.transfer(msg.sender,currentBoldBalance - initialBoldBalance) (src/Zappers/Modules/Exchanges/CurveExchange.sol#45) 
- [High] **unchecked-transfer**: WETHZapper.repayBold(uint256,uint256) (src/Zappers/WETHZapper.sol#113-128) ignores return value by boldToken.transferFrom(msg.sender,address(this),_boldAmount) (src/Zappers/WETHZapper.sol#122) 
- [High] **unchecked-transfer**: UniV3Exchange.swapFromBold(uint256,uint256) (src/Zappers/Modules/Exchanges/UniV3Exchange.sol#30-55) ignores return value by boldToken.transferFrom(msg.sender,address(this),_boldAmount) (src/Zappers/Modules/Exchanges/UniV3Exchange.sol#37) 
- [High] **unchecked-transfer**: BorrowerOperations.closeTrove(uint256) (src/BorrowerOperations.sol#682-749) ignores return value by WETH.transferFrom(gasPoolAddress,receiver,ETH_GAS_COMPENSATION) (src/BorrowerOperations.sol#741) 
- [High] **unchecked-transfer**: BorrowerOperations._openTrove(address,uint256,uint256,uint256,uint256,address,uint256,uint256,uint256,address,address,address,TroveChange) (src/BorrowerOperations.sol#297-379) ignores return value by WETH.transferFrom(msg.sender,gasPoolAddress,ETH_GAS_COMPENSATION) (src/BorrowerOperations.sol#376) 
- [High] **unchecked-transfer**: GasCompZapper.repayBold(uint256,uint256) (src/Zappers/GasCompZapper.sol#126-141) ignores return value by boldToken.transferFrom(msg.sender,address(this),_boldAmount) (src/Zappers/GasCompZapper.sol#135) 
- [High] **unchecked-transfer**: WETHZapper.closeTroveToRawETH(uint256) (src/Zappers/WETHZapper.sol#228-242) ignores return value by boldToken.transferFrom(msg.sender,address(this),trove.entireDebt) (src/Zappers/WETHZapper.sol#235) 
- [High] **unchecked-transfer**: WETHZapper.openTroveWithRawETH(IZapper.OpenTroveParams) (src/Zappers/WETHZapper.sol#20-78) ignores return value by boldToken.transfer(msg.sender,_params.boldAmount) (src/Zappers/WETHZapper.sol#71) 
- [Medium] **divide-before-multiply**: json.encode(bytes) (src/NFTMetadata/utils/JSON.sol#66-132) performs a multiplication on the result of a division: 	- result = new string(4 * ((data.length + 2) / 3)) (src/NFTMetadata/utils/JSON.sol#82) 
- [Medium] **divide-before-multiply**: TroveManager._redeemCollateralFromTrove(IDefaultPool,TroveManager.SingleRedemptionValues,uint256,uint256,uint256) (src/TroveManager.sol#672-715) performs a multiplication on the result of a division: 	- correspondingColl = _singleRedemption.boldLot * DECIMAL_PRECISION / _redemptionPrice (src/TroveManager.sol#685) 	- _singleRedemption.collFee = correspondingColl * _redemptionRate / DECIMAL_PRECISION (src/TroveManager.sol#687) 
- [Medium] **divide-before-multiply**: TroveManager._redistributeDebtAndColl(IActivePool,IDefaultPool,uint256,uint256) (src/TroveManager.sol#1086-1121) performs a multiplication on the result of a division: 	- boldDebtRewardPerUnitStaked = boldDebtNumerator / totalStakes (src/TroveManager.sol#1110) 	- lastBoldDebtError_Redistribution = boldDebtNumerator - boldDebtRewardPerUnitStaked * totalStakes (src/TroveManager.sol#1113) 
- [Medium] **divide-before-multiply**: numUtils.toLocaleString(uint256,uint8,uint8) (src/NFTMetadata/utils/Utils.sol#32-79) performs a multiplication on the result of a division: 	- fraction = (_value % 10 ** _divisor) / 10 ** (_divisor - _precision - 1) (src/NFTMetadata/utils/Utils.sol#47) 	- fraction = fraction * 10 (src/NFTMetadata/utils/Utils.sol#45) 	- fraction = fraction (src/NFTMetadata/utils/Utils.sol#45) 
- [Medium] **divide-before-multiply**: numUtils.toLocaleString(uint256,uint8,uint8) (src/NFTMetadata/utils/Utils.sol#32-79) performs a multiplication on the result of a division: 	- fraction = fraction * 10 ** (_precision - _divisor) (src/NFTMetadata/utils/Utils.sol#42) 	- fraction = (_value % 10 ** _divisor) / 10 ** (_divisor - _precision - 1) (src/NFTMetadata/utils/Utils.sol#47) 
- [Medium] **divide-before-multiply**: TroveManager._redistributeDebtAndColl(IActivePool,IDefaultPool,uint256,uint256) (src/TroveManager.sol#1086-1121) performs a multiplication on the result of a division: 	- collRewardPerUnitStaked = collNumerator / totalStakes (src/TroveManager.sol#1109) 	- lastCollError_Redistribution = collNumerator - collRewardPerUnitStaked * totalStakes (src/TroveManager.sol#1112) 
- [Medium] **unused-return**: TroveManager.redeemCollateral(address,uint256,uint256,uint256,uint256) (src/TroveManager.sol#747-845) ignores return value by (redemptionPrice,None) = priceFeed.fetchRedemptionPrice() (src/TroveManager.sol#775) 
- [Medium] **unused-return**: HybridCurveUniV3Exchange.swapFromBold(uint256,uint256) (src/Zappers/Modules/Exchanges/HybridCurveUniV3Exchange.sol#68-102) ignores return value by uniV3Router.exactInput(params) (src/Zappers/Modules/Exchanges/HybridCurveUniV3Exchange.sol#98) 
- [Medium] **unused-return**: HybridCurveUniV3Exchange.swapToBold(uint256,uint256) (src/Zappers/Modules/Exchanges/HybridCurveUniV3Exchange.sol#105-143) ignores return value by collToken.approve(address(uniV3Router),_collAmount) (src/Zappers/Modules/Exchanges/HybridCurveUniV3Exchange.sol#111) 
- [Medium] **unused-return**: UniV3Exchange.swapFromBold(uint256,uint256) (src/Zappers/Modules/Exchanges/UniV3Exchange.sol#30-55) ignores return value by boldToken.approve(address(uniV3RouterCached),_boldAmount) (src/Zappers/Modules/Exchanges/UniV3Exchange.sol#38) 
- [Medium] **unused-return**: CbbtcZapper._sendColl(address,uint256) (src/Zappers/CbbtcZapper.sol#31-33) ignores return value by IWrapper(collToken).withdrawTo(_receiver,_amount) (src/Zappers/CbbtcZapper.sol#32) 
- [Medium] **unused-return**: BaseZapper._requireZapperIsReceiver(uint256) (src/Zappers/BaseZapper.sol#44-47) ignores return value by (None,receiver) = borrowerOperations.removeManagerReceiverOf(_troveId) (src/Zappers/BaseZapper.sol#45) 
- [Medium] **unused-return**: MultiTroveGetter.getDebtPerInterestRateAscending(uint256,uint256,uint256) (src/MultiTroveGetter.sol#128-153) ignores return value by (None,prevId,interestBatchManager,None) = sortedTroves.nodes(currId) (src/MultiTroveGetter.sol#145) 
- [Medium] **unused-return**: CurveExchange.swapFromBold(uint256,uint256) (src/Zappers/Modules/Exchanges/CurveExchange.sol#34-47) ignores return value by boldToken.approve(address(curvePoolCached),_boldAmount) (src/Zappers/Modules/Exchanges/CurveExchange.sol#38) 
- [Medium] **unused-return**: GasCompZapper.receiveFlashLoanOnCloseTroveFromCollateral(IZapper.CloseTroveParams,uint256) (src/Zappers/GasCompZapper.sol#277-307) ignores return value by exchange.swapToBold(_effectiveFlashLoanAmount,trove.entireDebt) (src/Zappers/GasCompZapper.sol#289) 
- [Medium] **unused-return**: CbbtcZapper.constructor(IAddressesRegistry) (src/Zappers/CbbtcZapper.sol#19-22) ignores return value by _CBBTC.approve(address(collToken),type()(uint256).max) (src/Zappers/CbbtcZapper.sol#21) 
- [Medium] **unused-return**: UniV3Exchange.swapFromBold(uint256,uint256) (src/Zappers/Modules/Exchanges/UniV3Exchange.sol#30-55) ignores return value by uniV3RouterCached.exactOutputSingle(params) (src/Zappers/Modules/Exchanges/UniV3Exchange.sol#51) 
- [Medium] **unused-return**: HybridCurveUniV3Exchange.swapToBold(uint256,uint256) (src/Zappers/Modules/Exchanges/HybridCurveUniV3Exchange.sol#105-143) ignores return value by USDC.approve(address(curvePool),uniV3UsdcAmount) (src/Zappers/Modules/Exchanges/HybridCurveUniV3Exchange.sol#133) 
- [Medium] **unused-return**: WETHZapper.constructor(IAddressesRegistry,IFlashLoanProvider,IExchange) (src/Zappers/WETHZapper.sol#9-18) ignores return value by WETH.approve(address(borrowerOperations),type()(uint256).max) (src/Zappers/WETHZapper.sol#15) 
- [Medium] **unused-return**: TroveManager.urgentRedemption(uint256,uint256[],uint256) (src/TroveManager.sol#874-932) ignores return value by (price,None) = priceFeed.fetchPrice() (src/TroveManager.sol#883) 
- [Medium] **unused-return**: LeverageLSTZapper.constructor(IAddressesRegistry,IFlashLoanProvider,IExchange) (src/Zappers/LeverageLSTZapper.sol#13-19) ignores return value by boldToken.approve(address(_exchange),type()(uint256).max) (src/Zappers/LeverageLSTZapper.sol#18) 
- [Medium] **unused-return**: UniV3Exchange.swapToBold(uint256,uint256) (src/Zappers/Modules/Exchanges/UniV3Exchange.sol#57-84) ignores return value by collToken.approve(address(uniV3RouterCached),_collAmount) (src/Zappers/Modules/Exchanges/UniV3Exchange.sol#65) 
- [Medium] **unused-return**: HintHelpers.predictRemoveFromBatchUpfrontFee(uint256,uint256,uint256) (src/HintHelpers.sol#230-258) ignores return value by (None,None,None,None,None,None,None,None,batchManager,None) = troveManager.Troves(_troveId) (src/HintHelpers.sol#238) 
- [Medium] **unused-return**: ZapperAsFuck.constructor(IAddressesRegistry) (src/Zappers/ZapperAsFuck.sol#15-25) ignores return value by IERC20(address(collToken)).approve(address(borrowerOperations),type()(uint256).max) (src/Zappers/ZapperAsFuck.sol#24) 
- [Medium] **unused-return**: HybridCurveUniV3Exchange.swapFromBold(uint256,uint256) (src/Zappers/Modules/Exchanges/HybridCurveUniV3Exchange.sol#68-102) ignores return value by USDC.approve(address(uniV3Router),curveUsdcAmount) (src/Zappers/Modules/Exchanges/HybridCurveUniV3Exchange.sol#79) 
- [Medium] **unused-return**: ZapperAsFuck.constructor(IAddressesRegistry) (src/Zappers/ZapperAsFuck.sol#15-25) ignores return value by WETH.approve(address(borrowerOperations),type()(uint256).max) (src/Zappers/ZapperAsFuck.sol#22) 
- [Medium] **unused-return**: ActivePool.constructor(IAddressesRegistry) (src/ActivePool.sol#74-91) ignores return value by collToken.approve(defaultPoolAddress,type()(uint256).max) (src/ActivePool.sol#90) 
- [Medium] **unused-return**: DefaultPool.constructor(IAddressesRegistry) (src/DefaultPool.sol#35-46) ignores return value by collToken.approve(activePoolAddress,type()(uint256).max) (src/DefaultPool.sol#45) 
- [Medium] **unused-return**: CollateralRegistry.redeemCollateral(uint256,uint256,uint256) (src/CollateralRegistry.sol#92-175) ignores return value by (None,None,redeemable_scope_2) = troveManager_scope_1.getUnbackedPortionPriceAndRedeemability() (src/CollateralRegistry.sol#122) 
- [Medium] **unused-return**: GasCompZapper.constructor(IAddressesRegistry,IFlashLoanProvider,IExchange) (src/Zappers/GasCompZapper.sol#15-27) ignores return value by collToken.approve(address(borrowerOperations),type()(uint256).max) (src/Zappers/GasCompZapper.sol#24) 
- [Medium] **unused-return**: LeverageWETHZapper.constructor(IAddressesRegistry,IFlashLoanProvider,IExchange) (src/Zappers/LeverageWETHZapper.sol#10-16) ignores return value by boldToken.approve(address(_exchange),type()(uint256).max) (src/Zappers/LeverageWETHZapper.sol#15) 
- [Medium] **unused-return**: GasCompZapper.constructor(IAddressesRegistry,IFlashLoanProvider,IExchange) (src/Zappers/GasCompZapper.sol#15-27) ignores return value by collToken.approve(address(_exchange),type()(uint256).max) (src/Zappers/GasCompZapper.sol#26) 
- [Medium] **unused-return**: WETHZapper.receiveFlashLoanOnCloseTroveFromCollateral(IZapper.CloseTroveParams,uint256) (src/Zappers/WETHZapper.sol#274-302) ignores return value by exchange.swapToBold(_effectiveFlashLoanAmount,trove.entireDebt) (src/Zappers/WETHZapper.sol#286) 
- [Medium] **unused-return**: TroveManager.getUnbackedPortionPriceAndRedeemability() (src/TroveManager.sol#1217-1228) ignores return value by (price,None) = priceFeed.fetchPrice() (src/TroveManager.sol#1222) 
- [Medium] **unused-return**: WETHZapper.constructor(IAddressesRegistry,IFlashLoanProvider,IExchange) (src/Zappers/WETHZapper.sol#9-18) ignores return value by WETH.approve(address(_exchange),type()(uint256).max) (src/Zappers/WETHZapper.sol#17) 
- [Medium] **unused-return**: MainnetPriceFeedBase._getCurrentChainlinkResponse(AggregatorV3Interface) (src/PriceFeeds/MainnetPriceFeedBase.sol#78-105) ignores return value by (roundId,answer,updatedAt) = _aggregator.latestRoundData() (src/PriceFeeds/MainnetPriceFeedBase.sol#86-104) 
- [Medium] **unused-return**: BorrowerOperations.closeTrove(uint256) (src/BorrowerOperations.sol#682-749) ignores return value by (price,None) = priceFeed.fetchPrice() (src/BorrowerOperations.sol#720) 
- [Medium] **unused-return**: GasCompZapper.constructor(IAddressesRegistry,IFlashLoanProvider,IExchange) (src/Zappers/GasCompZapper.sol#15-27) ignores return value by WETH.approve(address(borrowerOperations),type()(uint256).max) (src/Zappers/GasCompZapper.sol#22) 
- [Medium] **unused-return**: GasPool.constructor(IAddressesRegistry) (src/GasPool.sol#19-28) ignores return value by WETH.approve(address(borrowerOperations),type()(uint256).max) (src/GasPool.sol#25) 
- [Medium] **unused-return**: WbtcZapper._sendColl(address,uint256) (src/Zappers/WbtcZapper.sol#31-33) ignores return value by IWrapper(collToken).withdrawTo(_receiver,_amount) (src/Zappers/WbtcZapper.sol#32) 
- [Medium] **unused-return**: HybridCurveUniV3ExchangeHelpers.getCollFromBold(uint256,IERC20,uint256) (src/Zappers/Modules/Exchanges/HybridCurveUniV3ExchangeHelpers.sol#75-97) ignores return value by (collAmount,None,None,None) = uniV3Quoter.quoteExactInput(path,curveUsdcAmount) (src/Zappers/Modules/Exchanges/HybridCurveUniV3ExchangeHelpers.sol#90) 
- [Medium] **unused-return**: WbtcZapper.constructor(IAddressesRegistry) (src/Zappers/WbtcZapper.sol#19-22) ignores return value by _WBTC.approve(address(collToken),type()(uint256).max) (src/Zappers/WbtcZapper.sol#21) 
- [Medium] **unused-return**: HintHelpers.predictAdjustTroveUpfrontFee(uint256,uint256,uint256) (src/HintHelpers.sol#132-162) ignores return value by (None,None,None,None,None,None,None,None,batchManager,None) = troveManager.Troves(_troveId) (src/HintHelpers.sol#142) 
- [Medium] **unused-return**: CurveExchange.swapToBold(uint256,uint256) (src/Zappers/Modules/Exchanges/CurveExchange.sol#49-64) ignores return value by collToken.approve(address(curvePoolCached),_collAmount) (src/Zappers/Modules/Exchanges/CurveExchange.sol#53) 
- [Medium] **unused-return**: MultiTroveGetter._getOneTrove(ITroveManager,uint256,IMultiTroveGetter.CombinedTroveData) (src/MultiTroveGetter.sol#58-86) ignores return value by (None,None,_out.stake,None,None,_out.lastDebtUpdateTime,None,None,_out.interestBatchManager,_out.batchDebtShares) = _troveManager.Troves(_id) (src/MultiTroveGetter.sol#72-83) 
- [Medium] **unused-return**: BorrowerOperations.constructor(IAddressesRegistry) (src/BorrowerOperations.sol#167-197) ignores return value by collToken.approve(address(activePool),type()(uint256).max) (src/BorrowerOperations.sol#196) 
- [Medium] **unused-return**: GasPool.constructor(IAddressesRegistry) (src/GasPool.sol#19-28) ignores return value by WETH.approve(address(troveManager),type()(uint256).max) (src/GasPool.sol#27) 
- [Medium] **unused-return**: TroveManager.batchLiquidateTroves(uint256[]) (src/TroveManager.sol#417-476) ignores return value by (price,None) = priceFeed.fetchPrice() (src/TroveManager.sol#429) 
- [Medium] **unused-return**: WbtcZapper._pullColl(uint256) (src/Zappers/WbtcZapper.sol#24-29) ignores return value by IWrapper(collToken).depositFor(address(this),collAmountInStrangeDecimals) (src/Zappers/WbtcZapper.sol#28) 
- [Medium] **unused-return**: CbbtcZapper._pullColl(uint256) (src/Zappers/CbbtcZapper.sol#24-29) ignores return value by IWrapper(collToken).depositFor(address(this),collAmountInStrangeDecimals) (src/Zappers/CbbtcZapper.sol#28) 
- [Medium] **unused-return**: HybridCurveUniV3Exchange.swapFromBold(uint256,uint256) (src/Zappers/Modules/Exchanges/HybridCurveUniV3Exchange.sol#68-102) ignores return value by boldToken.approve(address(curvePool),_boldAmount) (src/Zappers/Modules/Exchanges/HybridCurveUniV3Exchange.sol#74) 
