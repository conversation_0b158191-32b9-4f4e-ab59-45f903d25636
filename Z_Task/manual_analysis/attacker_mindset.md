# Smart Contract Vulnerability Hunting – System‑Thinking Playbook

> **Goal:** Equip auditors (human or AI) with a step‑by‑step mental model and checklist for detecting and proving *any* Ethereum smart‑contract bug while avoiding false positives.

---

## 1  Specification First

1. **Harvest Requirements** – Read all docs, READMEs, deployment scripts, and tests to list intended invariants, roles, and workflows.
2. **Build a Diff** – Compare intended vs. implemented behaviour; every mismatch is a bug candidate.

---

## 2  Threat‑Modelling Lenses

| Lens              | Guiding Question                                      |
| ----------------- | ----------------------------------------------------- |
| **Adversarial**   | If I only control an EO‑A, how can I maximise profit? |
| **State‑Machine** | Which transition breaks an invariant?                 |
| **Economic**      | Is the attack profitable after gas, MEV, slippage?    |

---

## 3  Storage‑Level Attacks

* Generate storage map using any tool or maybe using foundry.
* Hunt for **uninitialised slots**, **packing collisions**, **\_\_gap** misuse.
* Exploit pattern: `delegatecall` that overwrites privileged storage (e.g., `owner`).

---

## 4  ABI & Low‑Level Call Attacks

* Craft calldata via `abi.encodeWithSelector` or raw hex.
* Tweak dynamic‑type offsets/lengths to trigger OOB reads or logic inversion.
* Use `target.call{value:…}(data)` to bypass type safety. Always inspect `(success, data)`.

---

## 5  Invariant Verification

```bash
forge test --match-contract Invariant* -vvvv
```

1. Define protocol‑wide properties (`sum(balances) == totalSupply`).
2. Fuzz with realistic handlers; integrate latest symbolic tools use foundry tools where neccessary.

---

## 6  Classic Bug Classes & Quick Heuristics

| Class               | Heuristic                                      |
| ------------------- | ---------------------------------------------- |
| Reentrancy          | External call before state update, no guard.   |
| Access Control      | Roles not refreshed on upgrade / NFT transfer. |
| Math & Precision    | Division before multiplication ⇒ truncation.   |
| Oracle Manipulation | Single feed, long heartbeat.                   |
| Gas DoS             | Unbounded loop over user‑controlled data.      |

---

## 7  Cross‑Module & Economic Exploits

1. **Module Desync** – Vault updated, Strategy not.
2. **Fee Loops** – Deposit/withdraw cycles extract rewards.
3. **Privilege Chaining** – Harmless fn in A dangerous via proxy B.

---

## 8  PoC Checklist

* Read all on‑chain state; no hard‑coded magic.
* ≤10 tx on the testnet(or mainnet‑fork).
* Log pre/post balances & critical storage.
* Compute `profit – gas ≥ 0`.
* Cross‑validate with invariant test & manual trace.

---

## 9  Standard Report Template

1. **Title & Severity**
2. **Summary**
3. **Root Cause** (file\:line)
4. **Attack Steps** (tx table & state diffs)
5. **Impact (\$ & % TVL)**
6. **Recommended Fix** (code diff)

---

## 10  Quick Reference Checklist

* [ ] Docs/tests reviewed for invariants
* [ ] Storage layout mapped
* [ ] Public funcs fuzzed & invariants tested
* [ ] Oracle & MEV scenarios simulated
* [ ] Upgrade paths & role transfers checked
* [ ] Economic viability proven
