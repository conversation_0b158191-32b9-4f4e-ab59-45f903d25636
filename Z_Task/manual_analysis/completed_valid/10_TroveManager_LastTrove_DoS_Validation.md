# [Validation] TroveManager `OnlyOneTroveLeft` Denial-of-Service

**Status:** Confirmed

**Summary (≤5 lines):**
Victims become unable to close their Trove when it is the last remaining one.  `_closeTrove` enforces `_requireMoreThanOneTroveInSystem` *before* removing the caller’s Trove, so an attacker can leave the victim as the sole Trove and permanently block their withdrawal until a protocol shutdown occurs.

**Evidence**
1. Guard reverts if only one Trove exists:
```1136:1139:USDaf-v2/contracts/src/TroveManager.sol
function _requireMoreThanOneTroveInSystem(uint256 TroveIdsArrayLength) internal pure {
    if (TroveIdsArrayLength == 1) {
        revert OnlyOneTroveLeft();
    }
}
```
2. `_closeTrove` calls the guard *before* removing the Trove ID, letting the attacker pass but victim fail:
```1498:1507:USDaf-v2/contracts/src/TroveManager.sol
uint256 TroveIdsArrayLength = TroveIds.length;
// If branch not shut down (normal user close)
if (shutdownTime == 0 || closedStatus == Status.closedByLiquidation) {
    _requireMoreThanOneTroveInSystem(TroveIdsArrayLength);
}
_removeTroveId(_troveId, TroveIdsArrayLength);
```
3. Public flow `BorrowerOperations.closeTrove` routes to `_closeTrove` (see 312:330:USDaf-v2/contracts/src/BorrowerOperations.sol).

**Manual Validation Checklist Highlights**
- Business logic contradicts borrower expectation of always being able to exit.
- No privileged access required – any borrower can trigger.
- Gas cost negligible compared to damage.
- No reentrancy or oracle dependencies involved.

**Severity & Impact**
High severity under the bounty rubric: permanent denial of collateral redemption and repayment for innocent users, affecting liveness and causing potential total fund lock.

**Suggested Fix / Mitigation**
• Allow voluntary closure when `TroveIdsArrayLength == 1`, or
• Move the guard *after* `_removeTroveId`, ensuring the final closure path succeeds, or
• Automatically trigger protocol shutdown when penultimate Trove closes. 