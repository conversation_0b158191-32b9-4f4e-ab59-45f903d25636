# Validation Report: Urgent Redemption Unbacked Debt

**Status:** Confirmed

## Summary
A flaw in `TroveManager._urgentRedeemCollateralFromTrove()` allows redeemers to cap the collateral lot while paying proportionally *less* BOLD, leaving the Trove with zero collateral but residual debt. Each exploit cycle drains collateral at a ~1 % discount and leaves unbacked BOLD, leading to protocol insolvency in shutdown mode.

## Evidence
1. Bold lot is first limited by debt, then collateral lot is computed with bonus:
```847:857:USDaf-v2/contracts/src/TroveManager.sol
_singleRedemption.boldLot = LiquityMath._min(_maxBoldamount, _singleRedemption.trove.entireDebt);
_singleRedemption.collLot = _singleRedemption.boldLot * (DECIMAL_PRECISION + URGENT_REDEMPTION_BONUS) / _price;
```
2. If the collateral lot exceeds the Trove’s balance it is capped *without* increasing the bold lot proportionally:
```857:861:USDaf-v2/contracts/src/TroveManager.sol
if (_singleRedemption.collLot > _singleRedemption.trove.entireColl) {
    _singleRedemption.collLot = _singleRedemption.trove.entireColl;
    _singleRedemption.boldLot = _singleRedemption.trove.entireColl * _price / (DECIMAL_PRECISION + URGENT_REDEMPTION_BONUS);
}
```
3. The updated `boldLot` is lower than the Trove’s debt, so after `_applySingleRedemption` the Trove ends with `coll = 0` and `debt > 0`, creating uncompensated protocol liability:
```561:566:USDaf-v2/contracts/src/TroveManager.sol
uint256 newDebt = _singleRedemption.trove.entireDebt - _singleRedemption.boldLot; // residual debt remains
uint256 newColl = _singleRedemption.trove.entireColl - _singleRedemption.collLot; // becomes zero
```
4. Business logic contradicts documentation: redemptions must maintain full backing; leaving unbacked debt violates USDaf’s 110 % MCR and peg mechanism.

## Severity & Impact
**Critical** — Attackers can repeatedly drain collateral at a discount and accrue permanent unbacked BOLD. With multiple Troves the system becomes insolvent, breaking the USD peg and threatening total collateral loss. Impact is systemic and unrecoverable post-shutdown.

## Suggested Fix / Mitigation
When collateral lot is capped, *also* cap bold lot to the Trove’s *entire debt* so the position is fully closed, e.g.:
```solidity
if (collLot > entireColl) {
    collLot = entireColl;
    boldLot = entireDebt; // burn full debt if collateral is gone
}
```
Alternatively forbid urgent redemption on Troves whose ICR is below `1 + URGENT_REDEMPTION_BONUS` to guarantee the branch is unreachable.

---
*Validation performed following AI Agent Workflow Step-by-Step Prompt.* 