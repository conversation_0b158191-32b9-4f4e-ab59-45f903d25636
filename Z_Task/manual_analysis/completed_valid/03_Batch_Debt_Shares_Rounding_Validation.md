# Validation Report — Batch Debt Shares Rounding

**Status:** Confirmed  
**Report Under Review:** 02_Batch_Debt_Shares_Rounding.md

## Summary
I validated that `TroveManager._updateBatchShares()` allows debt changes that truncate `batchDebtSharesDelta` to zero due to Solidity integer division. This decouples `batch.totalDebtShares` from `batch.debt`. An attacker can loop tiny `repayBold()` calls followed by large `withdrawBold()` to obtain BOLD without proportional shares, leading to bad debt and potential insolvency.

## Evidence

1. Rounding to zero on debt decrease:

```1815:1830:USDaf-v2/contracts/src/TroveManager.sol
batchDebtSharesDelta = currentBatchDebtShares * debtDecrease / _batchDebt;
Troves[_troveId].batchDebtShares -= batchDebtSharesDelta;
batches[_batchAddress].debt = _batchDebt - debtDecrease;
```

2. Rounding to zero on debt increase:

```1800:1812:USDaf-v2/contracts/src/TroveManager.sol
batchDebtSharesDelta = currentBatchDebtShares * debtIncrease / _batchDebt;
Troves[_troveId].batchDebtShares += batchDebtSharesDelta;
batches[_batchAddress].debt = _batchDebt + debtIncrease;
```

3. No safeguard that `batchDebtSharesDelta > 0` when debt is modified, so any `debt{Increase,Decrease}` less than `_batchDebt / currentBatchDebtShares` rounds to zero while debt updates.

## Manual Validation Checklist

- Business logic: breaks invariant `debt / shares` = constant. ✔️  
- Access Control: regular borrower can trigger. ✔️  
- Math precision: integer division truncation. ✔️  
- Oracle manipulation not required. ✔️  
- No external calls — reentrancy not needed. ✔️  

## Severity & Impact

Using Cantina severity rubric, loss of collateral backing and protocol-wide bad debt without privileged access is **High Severity**. Financial impact can reach total TVL in the affected batch and cascade system-wide.

## Suggested Fix

1. Compute delta in fixed-point (e.g., RAY 1e27) and round up on debt decrease.  
2. `require(batchDebtSharesDelta > 0)` whenever debt is modified.  
3. Introduce minimum debt / share ratio and migrate batch when breached.

## Conclusion

The reported vulnerability is real, reproducible, and poses high risk. Immediate patch recommended. 