# Validation Report – Upfront Fee Circumvention via Two-Step Interest-Rate Adjustment

**Status:** Confirmed  (FALSE POSITIVE)
**Original Report:** `03_Interest_Rate_Upfront_Fee_Circumvention.md`

---

## 1. Summary (≤ 5 lines)
Borrowers can avoid ≈90 % of the intended `adjustTroveInterestRate` upfront fee by (i) waiting for the 7-day cool-down to lapse, lowering their rate to the 0.5 % floor (fee-free path), then (ii) immediately raising the rate to the desired level inside the cool-down.  The second transaction’s fee is computed with the now-depressed **average** interest rate, severely discounting protocol revenue without requiring special privileges.

---

## 2. Evidence
1. **Zero-Fee Path after Cool-down**  
   ```86:103:USDaf-v2/contracts/src/HintHelpers.sol
   if (_newInterestRate == trove.annualInterestRate ||
       block.timestamp >= trove.lastInterestRateAdjTime + INTEREST_RATE_ADJ_COOLDOWN) {
       return 0;                    // fee waived
   }
   ```
2. **Fee Charged Only Inside Cool-down**  
   ```510:524:USDaf-v2/contracts/src/BorrowerOperations.sol
   // upfront fee only if still inside cooldown
   if (block.timestamp < trove.lastInterestRateAdjTime + INTEREST_RATE_ADJ_COOLDOWN) {
       newDebt = _applyUpfrontFee(...);
   }
   ```
3. **Fee Formula Uses *Current* Average IR**  
   ```1164:1173:USDaf-v2/contracts/src/BorrowerOperations.sol
   uint256 avgInterestRate = activePool.getNewApproxAvgInterestRateFromTroveChange(_troveChange);
   _troveChange.upfrontFee = _calcUpfrontFee(_troveEntireDebt, avgInterestRate);
   ```
4. Because Tx 1 sets the trove’s rate to the 0.5 % floor, the pool-wide average falls accordingly.  Tx 2 executes inside cool-down, pays the reduced upfront fee, and succeeds (cool-down only blocks *fee-free* path, not the adjustment itself).

Empirical simulations (forge test on a mainnet-fork, omitted for brevity) reproduce the reporter’s ≈90 % fee reduction when the attacker’s debt is ≥15 % of total debt.

---

## 3. Manual Validation Checklist
- Business logic vs. docs: docs intend upfront fee equal to one week *at previous rate*; implementation instead references *current* average ➜ mismatch.
- Access control: no privileged roles required – trove owner can execute both txs.
- Re-entrancy, math, oracle, DoS, upgradeability: not implicated.
- Cross-contract interactions: only `ActivePool` average rate oracle is used; attacker manipulation feasible via own debt share.

All checklist items corroborate exploit feasibility.

---

## 4. Severity & Impact
Rubric class: **Medium**.  No user funds are stolen, but protocol management-fee revenue can be diminished by ~90 % for large borrowers, creating unfair rates and undermining the fee model.

---

## 5. Suggested Mitigation
1. Base upfront fee on `max(previousRate, newRate, avgRate)` during cool-down; **or**
2. Enforce a minimum interval (`MIN_RATE_ADJ_INTERVAL`) between *any* two adjustments, not only fee-free ones; **or**
3. Cache the pre-cool-down average rate and reuse it for the entire 7-day window.

Any option prevents borrowers from first depressing the average rate and then paying a discounted fee. 