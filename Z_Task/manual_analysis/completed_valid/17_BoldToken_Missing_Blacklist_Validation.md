Status: Confirmed

Summary:
The `BoldToken` contract fails to enforce the recipient blacklist promised in its inline documentation. As a result, any user can transfer USDaf tokens directly to core Liquity contracts (e.g., `TroveManager`, `StabilityPool`, `BorrowerOperations`, `ActivePool`), where the tokens become permanently irretrievable.

Evidence:
1. Contract comment explicitly states that transfer protection should blacklist core Liquity contracts:
```6:12:USDaf-v2/contracts/src/BoldToken.sol
 * 1) Transfer protection: blacklist of addresses that are invalid recipients (i.e. core Liquity contracts) in external
 * transfer() and transferFrom() calls. The purpose is to protect users from losing BOLD directly to a Liquity
 * core contract, when they should rather call the right function.
```

2. Actual implementation of `_requireValidRecipient` performs **no** blacklist checks—only zero-address and self checks:
```87:101:USDaf-v2/contracts/src/BoldToken.sol
function _requireValidRecipient(address _recipient) internal view {
    require(
        _recipient != address(0) && _recipient != address(this),
        "BoldToken: Cannot transfer tokens directly to the Bold token contract or the zero address"
    );
}
```

3. Core recipient example (`Trove<PERSON>anager`) has no function to forward/recover arbitrary ERC-20 tokens, meaning any USDaf sent there is stuck forever (excerpt shows lack of token recovery logic):
```1:30:USDaf-v2/contracts/src/TroveManager.sol
// TroveManager imports ...
contract TroveManager is LiquityBase, ITroveManager, ITroveEvents {
    // ... no ERC20 rescue / withdraw functions defined in 2000+ lines ...
}
```

Manual Validation Checklist:
- Business-logic mismatch confirmed between docs and code.
- Access control: missing blacklist allows unrestricted transfers to privileged contracts.
- No reentrancy/maths/oracle relevance.
- Upgradeability: contract is immutable; token loss is irreversible.

Severity & Impact:
Medium – Users can permanently lose their individual USDaf balances by transferring to forbidden Liquity contracts. The loss is limited to the sender’s tokens (does not drain protocol reserves) but is irreversible and likely given common user behaviour.

Suggested Fix / Mitigation:
Implement comprehensive blacklist inside `_requireValidRecipient`:
```
require(
    _recipient != address(0) && _recipient != address(this) &&
    !troveManagerAddresses[_recipient] &&
    !stabilityPoolAddresses[_recipient] &&
    !borrowerOperationsAddresses[_recipient] &&
    !activePoolAddresses[_recipient],
    "BoldToken: Invalid recipient"
);
```
Alternatively, surface a revert with an explicit error guiding users to the correct interaction path. 