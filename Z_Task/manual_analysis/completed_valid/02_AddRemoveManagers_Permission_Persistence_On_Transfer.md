# Validation Report – Privilege Persistence After TroveNFT Transfer

**Status:** Confirmed  

---

## Summary (≤ 5 lines)
A seller can assign themselves as `RemoveManager` for a trove via `setRemoveManagerWithReceiver()` and then transfer the associated `TroveNFT` to a buyer.  Because this mapping is **never cleared** on transfer, the former owner remains authorized to withdraw collateral or borrow debt, enabling complete theft of the buyer’s funds.

---

## Evidence
1. `removeManagerReceiverOf` mapping storing the privileged address:  
```23:34:USDaf-v2/contracts/src/Dependencies/AddRemoveManagers.sol
mapping(uint256 => RemoveManagerReceiver) public removeManagerReceiverOf;
```
2. The mapping is wiped **only** during liquidation, never on NFT transfer:  
```1189:1199:USDaf-v2/contracts/src/BorrowerOperations.sol
function _wipeTroveMappings(uint256 _troveId) internal {
    delete interestIndividualDelegateOf[_troveId];
    delete interestBatchManagerOf[_troveId];
    _wipeAddRemoveManagers(_troveId); // <-- called solely from onLiquidateTrove()
}
```
3. `TroveNFT` inherits vanilla `ERC721` and does **not** override transfer hooks to clear privileges:  
```1:25:USDaf-v2/contracts/src/TroveNFT.sol
contract TroveNFT is ERC721, ITroveNFT {
    ... // no _beforeTokenTransfer/_afterTokenTransfer override
}
```
4. Withdrawal-capable functions treat the `RemoveManager` as fully authorized:  
```573:583:USDaf-v2/contracts/src/BorrowerOperations.sol
if (_troveChange.collDecrease > 0 || _troveChange.debtIncrease > 0) {
    receiver = _requireSenderIsOwnerOrRemoveManagerAndGetReceiver(_troveId, owner);
}
```
Thus a non-owner `manager` can freely call `adjustTrove()` / `closeTrove()` after the NFT is sold.

---

## Severity & Impact
High-severity per bounty rubric:
* **Assets at risk:** Entire collateral and borrowing capacity of the trove.
* **Attack feasibility:** Any prior owner can exploit with a single privileged call; no admin oracles required.
* **Economic impact:** Up to 100 % of victim’s collateral (bounded only by trove size).

---

## Suggested Fix / Mitigation
1. **Automatic revocation on transfer** – Override `_beforeTokenTransfer` (or `_afterTokenTransfer`) in `TroveNFT` to invoke `BorrowerOperations._wipeAddRemoveManagers(troveId)` whenever `from != to`.
2. **Explicit reset tool** – Expose a public `clearManagers(uint256 troveId)` callable by the current owner.
3. **User education** – Until patched, warn users to reset manager mappings before purchasing troves.

---

*Validation completed following the Step-by-Step Vulnerability Validation Prompt.* 