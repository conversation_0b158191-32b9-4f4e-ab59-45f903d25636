# Validation Report – Stability Pool Stashed Collateral Not Reserved

**Status:** Confirmed ✅(FALSE-POSITIVE)

**Summary (≤ 5 lines)**
The StabilityPool treats each depositor’s `stashedColl` as though it were still liquid collateral by never deducting it from `collBalance`. Any user can therefore withdraw/claim other users’ deferred collateral, reducing `collBalance` below the victim’s recorded `stashedColl`. Subsequent calls to `claimAllCollGains()` by the victim revert permanently, locking their funds while the attacker receives an unjustified payout.

---

## Evidence (code & reasoning)
1. `stashedColl` added but **not** reserved:
```318:331:USDaf-v2/contracts/src/StabilityPool.sol
if (_doClaim) {
    newStashedColl = 0;
    collToSend = stashedColl[_depositor] + _currentCollGain;
} else {
    newStashedColl = stashedColl[_depositor] + _currentCollGain; // ↑ increases stash
    collToSend = 0;                     // collBalance untouched
}
```
2. `getDepositorCollGain` uses full `collBalance` when rewarding others:
```455:462:USDaf-v2/contracts/src/StabilityPool.sol
return LiquityMath._min(initialDeposit * normalizedGains / snapshots.P, collBalance);
```
3. Final transfer subtracts directly from `collBalance` without checking stash:
```571:579:USDaf-v2/contracts/src/StabilityPool.sol
uint256 newCollBalance = collBalance - _collAmount; // under-flow if insufficient
collBalance = newCollBalance;
collToken.safeTransfer(msg.sender, _collAmount);
```
4. Victim withdrawal path where their deposit is zero hits:
```334:348:USDaf-v2/contracts/src/StabilityPool.sol
uint256 collToSend = stashedColl[msg.sender];
_requireNonZeroAmount(collToSend);
stashedColl[msg.sender] = 0;
_sendCollGainToDepositor(collToSend); // reverts if collBalance < collToSend
```
5. No update to `collBalance` elsewhere offsets the stash; thus an attacker who deposits a large amount and claims with `_doClaim = true` can legally transfer the victim’s reserved collateral, drive `collBalance` low, and brick the victim’s claim function.

---

## Manual Validation Checklist
- **Business-logic vs docs:** Docs imply each user’s gains are protected; code fails to reserve them ✔
- **Access control:** Any EOAs can exploit via normal deposit/claim ✔
- **Re-entrancy / external calls:** Not required ✔
- **Math errors:** Underflow in `_sendCollGainToDepositor` → revert ✔
- **Oracle, gas-grief, upgradeability:** N/A

All checklist items corroborate exploit feasibility with no privileged access.

---

## Severity & Impact
Using Cantina rubric this is **High severity**: arbitrary users can cause permanent loss/DoS of collateral gains for others and extract that value. Profit scales with stashed amounts; gas cost negligible.

---

## Suggested Fix / Mitigation
Maintain a `totalStashedColl` variable and compute
```solidity
uint256 availableColl = collBalance - totalStashedColl;
```
when distributing rewards. Additionally, cap `_sendCollGainToDepositor` with a pre-check `require(collBalance >= _collAmount)` to fail early instead of locking users. 