 

**Status:** Confirmed  

**Summary (≤ 5 lines):**  
A first-depositor can capture **all** accumulated BOLD yield in the Stability Pool when the pool balance is below `MIN_BOLD_IN_SP` by depositing the minimum 1 BOLD.  The depositor’s snapshot is taken *before* pending yield is converted and distributed, giving them 100 % of the rewards and diluting later users.

---

## Evidence
1. Deposit snapshot **before** yield distribution:
```232:250:USDaf-v2/contracts/src/StabilityPool.sol
_updateDepositAndSnapshots(msg.sender, newDeposit, newStashedColl);
// ... existing code ...
_updateYieldRewardsSum(0);
```
2. `_updateYieldRewardsSum` holds rewards while `totalBoldDeposits < MIN_BOLD_IN_SP`, then distributes once the attacker’s deposit raises the balance:
```270:292:USDaf-v2/contracts/src/StabilityPool.sol
if (totalBoldDeposits < MIN_BOLD_IN_SP) {
    yieldGainsPending = accumulatedYieldGains; // rewards parked
    return;
}
// later call with attacker deposit now > threshold distributes to attacker only
```
3. Reported attack sequence matches logic flow; no guard prevents minimal deposit exploitation.

---

## Severity & Impact
According to the competition rubric, any bug enabling theft of protocol funds without privileged access is **High** severity.  An attacker can drain the full `yieldGainsPending` balance (unbounded, limited only by time the pool stayed empty) for the cost of depositing and later withdrawing 1 BOLD.

Financial impact: Potential loss equals entire pending yield, which may accumulate to hundreds or thousands of BOLD, directly harming future depositors and undermining pool incentives.

---

## Suggested Fix / Mitigation
Move the pending-yield conversion call **before** recording the new depositor’s snapshot, e.g.:
```solidity
// distribute any pending yield using pre-deposit balances
_updateYieldRewardsSum(0);

// then record new depositor state
_updateDepositAndSnapshots(msg.sender, newDeposit, newStashedColl);
```
Alternatively, require a grace period before new depositors earn on yield minted prior to their first deposit.
