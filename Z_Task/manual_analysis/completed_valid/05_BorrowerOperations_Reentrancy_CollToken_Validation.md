# Validation Report: Re-entrancy in `BorrowerOperations.adjustTrove` via Collateral Token Callback

## Status
Confirmed ✅ (INVALID VULNERABILITY)

## Summary (≤ 5 lines)
`BorrowerOperations.adjustTrove()` finalises trove state in `TroveManager` and `ActivePool` **before** pulling collateral tokens with `safeTransferFrom`.  A malicious ERC-777–style collateral can re-enter `adjustTrove()` during the token transfer, recursively inflating debt while providing the same collateral only once and leaving the trove (and therefore the system) severely under-collateralised.

## Evidence
1. **State update precedes external call**
```670:678:USDaf-v2/contracts/src/BorrowerOperations.sol
_troveManager.onAdjustTrove(_troveId, vars.newColl, vars.newDebt, _troveChange);

vars.activePool.mintAggInterestAndAccountForTroveChange(_troveChange, batchManager);
_moveTokensFromAdjustment(receiver, _troveChange, vars.boldToken, vars.activePool);
```
2. **External token transfer enabling re-entrancy**
```1294:1301:USDaf-v2/contracts/src/BorrowerOperations.sol
// Pull coll tokens – can call back into BorrowerOperations via token hook
collToken.safeTransferFrom(msg.sender, address(_activePool), _amount);
```
3. **No re-entrancy guard** – contract lacks `nonReentrant` modifier or equivalent mutex.
4. **Checks-Effects-Interactions violated** – critical effects (state mutations) occur before interaction with external token contract.

## Severity & Impact
**High** – An unprivileged attacker can recursively borrow BOLD to drain system collateral or push TCR below CCR triggering global insolvency.  Attack cost is minimal gas; profit bounded by system debt capacity.

## Suggested Fix / Mitigation
* Apply OpenZeppelin `ReentrancyGuard` (`nonReentrant`) to all external functions performing token transfers.
* Re-order operations to *pull* collateral **before** updating state (`checks-effects-interactions`).
* Optionally restrict collateral tokens to ERC-20 implementations without user-defined hooks (reject ERC-777/`tokensReceived`). 