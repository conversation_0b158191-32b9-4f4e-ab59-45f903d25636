# Validation Report: Redemption DoS via Dust Trove Flooding

**Status:** Confirmed

---

## Summary (≤&nbsp;5 lines)
A redemption-denial attack exists in `TroveManager.redeemCollateral()` where the function iterates once per Trove until the requested BOLD amount is satisfied. By creating thousands of minimal-debt ("dust") Troves, an attacker can force the loop to consume more gas than the block limit, rendering all ordinary redemption attempts impossible.

---

## Evidence
1. Unbounded loop per Trove:
```744:768:USDaf-v2/contracts/src/TroveManager.sol
while (singleRedemption.troveId != 0 && vars.remainingBold > 0 && _maxIterations > 0) {
    _maxIterations--;                       // user-supplied or ∞ when 0
    ...                                     // heavy accounting + external calls
}
```
2. User-controlled `_maxIterations` allows unlimited iterations when callers pass `0`:
```120:147:USDaf-v2/contracts/src/CollateralRegistry.sol
function redeemCollateral(uint256 _boldAmount, uint256 _maxIterationsPerCollateral, uint256 _maxFeePercentage) external {
    ...
    uint256 redeemedAmount = troveManager.redeemCollateral(
        msg.sender,
        redeemAmount,
        prices[index],
        redemptionRate,
        _maxIterationsPerCollateral   // ⚠️  forwarded unchanged
    );
```
3. No upper bound on list length; each Trove causes several external calls, making gas cost linear in attacker-controlled Trove count.

---

## Severity & Impact
* **Severity:** **High** (per bounty rubric: loss of core functionality / peg risk).
* **Financial Impact:** Redemptions freeze → market peg breaks; TVL (hundreds of millions) becomes illiquid.
* **Likelihood:** Medium – capital required but largely recoverable, making attack economically feasible.

---

## Suggested Fix / Mitigation
* Enforce a hard cap on iterations inside `redeemCollateral` independent of user input (e.g., 50).
* Require a minimum debt cancelled per iteration (e.g., 10 000 BOLD) or skip the Trove.
* Adopt hint-based redemptions to jump directly to sizeable Troves, following Liquity v1’s `firstRedemptionHint` pattern. 