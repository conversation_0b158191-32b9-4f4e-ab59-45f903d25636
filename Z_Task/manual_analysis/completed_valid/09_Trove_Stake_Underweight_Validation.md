# 09 – Trove Stake Underweight After Interest-Rate Adjustment – Validation Report

**Status:** Confirmed (High Severity)

---

## 1. Summary (≤ 5 lines)
`TroveManager.onAdjustTroveInterestRate()` moves pending redistribution gains into the trove’s collateral **without updating its `stake`**.  Future liquidation rewards are allocated per-unit-stake, so the trove is subsequently charged **less debt than warranted**, diluting all other troves and enabling value extraction with repeated calls to `BorrowerOperations.adjustTroveInterestRate()`.

---

## 2. Evidence

| Step | Evidence (file & lines) | Notes |
|------|-------------------------|-------|
| 1 | ```1361:1386:USDaf-v2/contracts/src/TroveManager.sol``` | `onAdjustTroveInterestRate()` updates `coll`, `debt`, `annualInterestRate`, **but not `stake`**. |
| 2 | ```1417:1424:USDaf-v2/contracts/src/TroveManager.sol``` | Comparison: `onAdjustTrove()` path **does** call `_updateStakeAndTotalStakes(...)`. |
| 3 | `_updateStakeAndTotalStakes` definition at ```1049:1065:TroveManager.sol``` shows new stake is derived from collateral and updates `totalStakes`. |
| 4 | Redistribution math (`L_boldDebt`, `L_coll`) uses `stake` (see ```964:966:TroveManager.sol```).  Under-stated stake ⇒ reduced future debt allocation. |
| 5 | Caller freedom: `adjustTroveInterestRate()` is externally callable by trove owner and [_requireIsNotInBatch_] only (see ```509:548:BorrowerOperations.sol```).  No privileged access required. |

---

## 3. Manual Checklist Results

1. **Business-logic consistency** – Violates invariant that `stake` mirrors collateral; confirmed.  
2. **Access control** – Any trove owner can invoke; no mitigation.  
3. **Re-entrancy / ordering** – Not applicable.  
4. **Math / precision** – Stake mis-weight leads to systemic mis-pricing.  
5. **Oracle vectors** – Irrelevant.  
6. **DoS / Gas griefing** – None.  
7. **Upgradeability quirks** – Contracts are immutable; bug is permanent.  
8. **Cross-contract assumptions** – Redistribution formulas rely on accurate stakes – violated here.

---

## 4. Severity & Impact
Using Cantina rubric: any bug allowing unilateral economic gain + dilution of all users is **High**.  
• **Impact:** Attacker avoids debt proportional to extra collateral, siphoning value system-wide.  
• **Likelihood:** High – callable at will, low gas, repeatable.  
Estimated profit equals the accumulated collateral gains * (1 – newStake/trueStake); with enough liquidations attacker can drain significant system collateral, threatening solvency.

---

## 5. Suggested Fix / Mitigation
Insert stake refresh inside `onAdjustTroveInterestRate` right after collateral update:
```solidity
uint256 newStake = _updateStakeAndTotalStakes(_troveId, _newColl);
```
and emit updated stake in the event.

Add unit test:  
1. Create trove, trigger liquidation to accrue  rewards.  
2. Call `adjustTroveInterestRate`, assert `stake == _newColl`.

---

## 6. Validation Sign-off
All claims in the original report are reproducible in code review; vulnerability **valid**. 