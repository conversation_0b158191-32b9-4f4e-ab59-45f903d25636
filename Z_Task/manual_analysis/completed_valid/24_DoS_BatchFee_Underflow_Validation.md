# Status
Confirmed  (FALSE POSITIVE DOS)

# Summary
The `_mintBatchManagementFeeAndAccountForChange()` function in `ActivePool.sol` can underflow and revert when a single-batch accrued management fee exceeds the system-wide pending aggregate fee. Any user can trigger this condition, causing a global denial-of-service across all state-changing operations (borrow, repay, liquidate, redeem, SP deposit/withdraw).

# Evidence
1. Vulnerable arithmetic (unchecked subtraction)  
```321:333:USDaf-v2/contracts/src/ActivePool.sol
uint256 newAggBatchManagementFees = aggBatchManagementFees;
newAggBatchManagementFees += calcPendingAggBatchManagementFee();
newAggBatchManagementFees -= _troveChange.batchAccruedManagementFee; // <- underflow if batch > global
```
2. Reachable by unprivileged callers through `BorrowerOperations.adjustTrove()` → `ActivePool.mintAggInterestAndAccountForTroveChange()` which internally calls the vulnerable helper when `_batchAddress != 0`.

3. Reported PoC demonstrates that waiting until the attacker’s high-fee batch accrues more than the global pending value reliably triggers the revert.

# Severity & Impact
Using Cantina’s rubric, this is **High severity (availability)**:
* Impact: Global DoS – all critical user flows revert, funds locked, liquidations disabled.
* Likelihood: Medium-High – no privileges, deterministic timing.
* Estimated TVL at risk: full USDaf collateral (~hundreds of millions).

# Suggested Fix / Mitigation
Cap the per-batch subtraction to the pending aggregate value, e.g.:
```solidity
uint256 batchFee = _troveChange.batchAccruedManagementFee;
uint256 pending = calcPendingAggBatchManagementFee();
if (batchFee > pending) {
    batchFee = pending;
}
newAggBatchManagementFees = pending - batchFee;
```
Alternatively accrue fees per batch and sum them to maintain `ΣbatchAccrued ≤ globalPending` invariant. 