# [Validated] Split Redemptions Enable Significant Fee Circumvention

**Status**: Confirmed

**Summary**  
The vulnerability allows users to reduce effective redemption fees by splitting large redemptions into multiple smaller transactions. This bypasses the intended quadratic fee structure, reducing fees by up to ~50% for large amounts and weakening the anti-whale mechanism.

**Evidence**  
- The redemption rate is calculated before updating the base rate: ```144:144:USDaf-v2/contracts/src/CollateralRegistry.sol  
uint256 redemptionRate = _calcRedemptionRate(  
    _getUpdatedBaseRateFromRedemption(_boldAmount, totals.boldSupplyAtStart)  
);  
```  
- The base rate is only updated after the redemption: ```168:168:USDaf-v2/contracts/src/CollateralRegistry.sol  
_updateBaseRateAndGetRedemptionRate(totals.redeemedAmount, totals.boldSupplyAtStart);  
```  
- Base rate update formula introduces the quadratic component: ```254:260:USDaf-v2/contracts/src/CollateralRegistry.sol  
function _getUpdatedBaseRateFromRedemption(uint256 _redeemAmount, uint256 _totalBoldSupply)  
    internal  
    view  
    returns (uint256) {  
    uint256 decayedBaseRate = _calcDecayedBaseRate();  
    uint256 redeemedBoldFraction = _redeemAmount * DECIMAL_PRECISION / _totalBoldSupply;  
    uint256 newBaseRate = decayedBaseRate + redeemedBoldFraction / REDEMPTION_BETA;  
    newBaseRate = LiquityMath._min(newBaseRate, DECIMAL_PRECISION);  
    return newBaseRate;  
}  
```  
- Mathematical verification confirms fee reduction from splitting matches the report's formulas.

**Severity & Impact**  
- **Severity**: Medium (per report and standard rubrics: economic impact without direct theft, high likelihood).  
- **Impact**: Protocol revenue loss up to 50% on large redemptions; weakens anti-whale protections; potential for abuse by sophisticated users. Realistic for whales redeeming 40%+ of supply, with impact scaling with TVL (target $24B market).

**Suggested Fix / Mitigation**  
1. Update base rate before calculating redemption rate in each call.  
2. Compute fees incrementally during redemption.  
3. Implement rate limits or delays between redemptions from the same address to make splitting uneconomical. 