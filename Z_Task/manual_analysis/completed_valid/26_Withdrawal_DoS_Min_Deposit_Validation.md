# StabilityPool.withdrawFromSP Minimum Deposit DoS — Validation Report

## Status
Confirmed

## Summary (≤5 lines)
A hard‐coded 1 BOLD floor (`MIN_BOLD_IN_SP`) inside `StabilityPool.withdrawFromSP()` prevents any withdrawal that would bring `totalBoldDeposits` below this value.  An attacker can lock other users’ funds by adding a trivial deposit and refusing to withdraw, thereby forcing the `require` check to revert when victims try to exit fully.

## Evidence

1. Constant defining the 1 BOLD floor:
```80:87:USDaf-v2/contracts/src/Dependencies/Constants.sol
uint256 constant MIN_BOLD_IN_SP = 1e18;
```

2. Guard that reverts if pool balance would fall below the floor:
```310:317:USDaf-v2/contracts/src/StabilityPool.sol
require(newTotalBoldDeposits >= MIN_BOLD_IN_SP, "With<PERSON><PERSON> must leave totalBoldDeposits >= MIN_BOLD_IN_SP");
```

3. Attack flow reproduced from the original report remains valid – no additional checks prevent a dust attacker deposit (e.g., 0.0001 BOLD) followed by a victim’s full withdrawal attempt.

## Severity & Impact
High — Denial-of-withdrawal leads to permanent lock of victim capital until attacker cooperates or a third party tops-up the pool.  Exploit costs dust (<$0.01), requires no privileged role, and affects all Stability Pool users.

## Suggested Fix / Mitigation
1. Remove the hard floor altogether and allow `totalBoldDeposits` to reach zero, mirroring Liquity v2 reference implementation.
2. Alternatively, lower the constant to a negligible “dust” level (e.g., 1 wei) or implement elastic logic that automatically withdraws the maximum amount that still satisfies the floor, returning the remainder later. 