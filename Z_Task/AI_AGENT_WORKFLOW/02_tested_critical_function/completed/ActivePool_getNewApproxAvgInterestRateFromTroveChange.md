## Function: ActivePool.getNewApproxAvgInterestRateFromTroveChange

### 📍 **Source & Test Locations**
- **Source Path**: `USDaf-v2/contracts/src/ActivePool.sol:125`
- **Test Path**: `USDaf-v2/contracts/test/interestRateAggregate.t.sol:2290` (`getNewApproxAvgInterestRateFromTroveChange`)
- **Contract**: `ActivePool`
- **Function Signature**: `function getNewApproxAvgInterestRateFromTroveChange(TroveChange calldata _troveChange) external view returns (uint256)`

### 🧪 **Test Coverage Analysis**
- **Scenario**: Test crafts synthetic `TroveChange` structs and asserts returned average interest rate matches hand-computed expectation after pending aggregates.
- **Security Implication**: Critical for fee prediction and interest accrual; incorrect value could leak or overcharge interest across system.
- **Assertions**: Maintains invariant `avgIR = aggWeightedDebtSum / aggRecordedDebt` post-change. 