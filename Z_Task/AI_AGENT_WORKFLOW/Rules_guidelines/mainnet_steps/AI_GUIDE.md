what are the steps t demonstrate it effectively, to show the vulnerability on a fork mainnet, you can modify this file here /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/2025-06-symbiotic-relay/middleware-sdk/test/vulnerabilities/Vault_Donation_ShareManipulation.t.sol, because, the test here is already compiling and working fine, you just need to modify it to work with mainnet fork,

you can learn how immunefi demonstration requirement is in here  /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/Task/AI_AGENT_WORKFLOW/Rules_guidelines/mainnet_steps/PoC_Rules.md, skip other step and focus on the main POC demonstration, no fake, mock (except if it is neccessary, even though it is neccessary, use the one inbuilt by the project), or anything that could make this a fase-positive vuknerability, make sure there is real interaction with the vulnerable code, learn more from the deployment script, follow step by step using a plan or to-do list. make sure you effectively demonstration the vulnerability and show impact, here is the contract address @https://etherscan.io/address/******************************************?utm_source=immunefi#code 