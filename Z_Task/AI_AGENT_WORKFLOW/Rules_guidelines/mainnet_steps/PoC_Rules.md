# 📜 Rules for Crafting a Runnable Proof-of-Concept (PoC)
_Aligned with Immunefi PoC Guidelines & common BBP requirements_

---

## 1. Confirm Scope First
1. Copy the **Impacts in Scope** bullet(s) from the bounty page.  
2. List the **Assets in Scope** (contract addresses & repo paths).  


---

## 2. Environment Requirements
- **Mainnet Fork Only** (or the project's official test suite).  
- Accepted framework: **Foundry**.  
- Disclose every dependency (paste `package.json` or `foundry.toml` snippet).  
- Declare environment variables (`RPC_URL`, `BLOCK_NUMBER`, private keys).  
- Do **not** broadcast transactions on public testnet/mainnet.

---

## 3. Structure of the PoC
1. **Setup Script / Test**  – one command should bootstrap and run everything:
   ```bash
   git clone <repo> && cd <repo>
   npm install          # or `forge install`
   export RPC_URL=<...>
   export BLOCK_NUMBER=<...>
    test/vulnerabilities`forge test -vv`  
   ```
2. **Clear Output**: print balances or state before & after exploit.  
3. **Assertions**: include `assert` / `require` statements proving success.  
4. **Comments / Logs**: describe each step (e.g., "step-1 borrow flash loan", " step-2 manipulate oracle").

---

## 4. Mandatory Sections in the Report
| Section | Minimum Content |
|---------|-----------------|
| Metadata | Program, network, contract addresses, bug class, severity, submission date |
| Vulnerability Summary | 1-paragraph overview of flaw & impact |
| Reproduction Steps | Human-readable, step-by-step alongside the test file |
| PoC Code | Full source or link to zip/drive; **no screenshots** |
| Impact & Funds at Risk | Token count × price = USD value; cite price source |
| Mitigation (optional) | Suggested fix or pattern |
| Compliance Checklist | Tick-box list confirming every rule here |

---

## 5. Compliance Checklist (include in your report)
- [ ] Uses **mainnet fork** or official tests
- [ ] Lists **all dependencies & env vars**
- [ ] Provides **runnable code**, not images
- [ ] Contains **print statements/logs** showing exploit
- [ ] Calculates **funds at risk**
- [ ] Executes **no on-chain DoS** without permission
- [ ] Respects all **program-specific rules**

> Reports lacking any item above may be rejected or require resubmission.

---

## 6. Best Practices
• Use deterministic block numbers to avoid state drift.  
• Keep the PoC minimal—remove dead code & large binaries.  
• Comment liberally so reviewers can follow logic quickly.  
• Link directly to permalinks for any referenced code lines.

---

## 7. PoC Code Author Checklist
_Ensure **every** item below is satisfied before submission._

- [ ] **Foundry Test File**: at least one `forge-std`-based test contract inside the `test/` directory.
- [ ] **Mainnet Fork Setup**: `setUp()` uses `vm.createSelectFork(RPC_URL, BLOCK_NUMBER)` with deterministic block.
- [ ] **Step-by-Step Comments**: inline comments explain each exploit phase.
- [ ] **Verbose Logs**: `emit log_named_decimal_uint` / `console.log` print critical balances or state before & after actions.
- [ ] **Critical Assertions**: `assertEq`, `assertGt`, or similar statements proving exploit success.
- [ ] **Funds-at-Risk Calculation**: test logs or comments show token amount × price ⇒ USD value.
- [ ] **No Secrets Committed**: private keys loaded via `vm.envUint` / `vm.envString`, never hard-coded.
- [ ] **Minimal & Self-Contained**: remove dead code, binaries, artifacts; rely only on declared dependencies.
- [ ] **License & Pragma**: file starts with SPDX identifier and `pragma solidity ^0.8.x`.
- [ ] **Deterministic Addresses**: constants for target contracts match scoped assets; avoid magic numbers.
- [ ] **Imports Resolve Locally**: all `import` paths compile without additional steps.

> If any box remains unticked, the PoC may be deemed incomplete and could be rejected or sent back for revisions.

---

Follow these rules and your PoC will be review-ready, demonstrably impactful, and fast to validate. 