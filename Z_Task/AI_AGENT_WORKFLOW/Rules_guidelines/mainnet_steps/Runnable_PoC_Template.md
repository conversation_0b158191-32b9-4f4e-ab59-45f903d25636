# 🛠️ Runnable PoC Report Template  
_Compliant with Immunefi PoC Guidelines & Typical Smart-Contract BBP Rules_

---

## 0. Report Metadata  
| Field | Value (fill all) |
|-------|------------------|
| Program | <!-- Project / Protocol name --> |
| Network | <!-- e.g. Ethereum Mainnet --> |
| Contract(s) | <!-- Deployed addresses & filenames --> |
| Bug Class | <!-- e.g. Re-entrancy, Access Control, Economic --> |
| Severity Est. | <!-- Critical / High / Medium / Low --> |
| Submission Date | <!-- YYYY-MM-DD --> |

---

## 1. Scope Confirmation  
1. **Impacts in Scope**: `<!-- quote exact impact bullet(s) from BBP \"Impacts in Scope\" -->`  
2. **Assets in Scope**: `<!-- copy contract address / repo path as listed -->`  
3. This report **does not violate any Out-of-Scope rules** and abides by all custom/standard Immunefi requirements.

---

## 2. Vulnerability Summary  
> _Concise, one-paragraph description of the flaw and why it matters._

---

## 3. Proof-of-Concept Environment  
```bash
# ✅ Reproducible in a single shell (example)
# Clone repo and install deps
$ git clone <repo> vuln-poc && cd vuln-poc
$ npm install                # or `forge install`

# Mainnet-fork settings
# (Use the **latest commit & exact block-number** that matches deployed contracts)
export RPC_URL=<your-node-url>
export BLOCK_NUMBER=<block>

# Run test
$ npx hardhat test test/Exploit.t.js  | cat
```

| Tool       | Version |
|------------|---------|
| Hardhat    | <!-- x.y.z --> |
| Foundry    | <!-- optional --> |
| Node       | <!-- x.y.z --> |
| Solidity   | <!-- pragma --> |

**Dependencies**  
List every library/plugin used (package.json or foundry.toml snippet). Ensure a reader can `npm install` / `forge install` without extra effort.

---

## 4. Step-by-Step Reproduction Instructions  
1. **Setup mainnet fork** at block `<!-- ###### -->`.  
2. Deploy or interface with target contract(s).  
3. Execute exploit transaction(s) **exactly as scripted** in PoC.  
4. Observe printed output demonstrating:  
   * State before exploit (balances / roles / storage).  
   * Action taken.  
   * State after exploit.  
5. Final assertion: funds stolen/frozen ≈ **`$<value>`**.

> **Note:** All steps reproduced via the provided test file; running the command in §3 yields identical output.

---

## 5. PoC Code  
_Place full, runnable code below or link to a zip/Google-Drive containing the entire Hardhat/Foundry project._

<details>
<summary>Click to view PoC test file</summary>

```solidity
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

// ... example imports ...
import "forge-std/Test.sol";

contract Exploit is Test {
    address constant TARGET = 0x...;

    function setUp() public {
        vm.createSelectFork(vm.envString("RPC_URL"), vm.envUint("BLOCK_NUMBER"));
    }

    function testExploit() public {
        /* 1. show initial balances */
        emit log_named_decimal_uint("Victim balance before", TARGET.balance, 18);

        /* 2. perform exploit */
        // ... exploit code ...

        /* 3. assert and print results */
        emit log_named_decimal_uint("Victim balance after", TARGET.balance, 18);
        assertGt(TARGET.balance, 0, "Funds drained");
    }
}
```

</details>

---

## 6. Impact & Funds at Risk  
* **Total tokens affected**: `<!-- integer -->`  
* **Token price**: `$<!-- price -->` (source link)  
* **Potential loss**: **`$<!-- amount -->`**  
* Broader protocol implications: `<!-- oracle manipulation, governance takeover, etc. -->`

---

## 7. Recommended Mitigation  
_Optional but appreciated: suggest specific code patches or pattern changes._

---

## 8. Compliance Checklist (must be **all ✅**)
- [ ] PoC uses **mainnet fork** or project's official test suite.
- [ ] All **dependencies & env vars** documented.
- [ ] Includes **runnable code**, no screenshots.
- [ ] Contains **clear print statements** showing exploit success.
- [ ] Provides **funds-at-risk calculation**.
- [ ] **No public testnet/mainnet** transactions executed.
- [ ] **No DoS** performed without prior written approval.
- [ ] Respects every **program-specific rule**.

> Submitters: keep this checklist in your report. Reviewers can quickly verify compliance.

---

### 🔖 Appendix
* **References**: link to exact code lines (GitHub permalink).
* **Assumptions checked**: enumerate any protocol invariants relied upon.
* **Versioning**: commit hashes & contract bytecode metadata. 