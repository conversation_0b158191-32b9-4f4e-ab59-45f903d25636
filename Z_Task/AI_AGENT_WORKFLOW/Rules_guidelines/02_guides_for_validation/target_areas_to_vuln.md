How to pick the **right** functions to test (and avoid dead-ends)

1. Trace the real deployment path first  
   • Open the broadcast script – here `1:171:2025-06-symbiotic-relay/middleware-sdk/lib/core/script/deploy/Vault.s.sol`.  
   • See it calls `VaultConfigurator.create()`.  
   • Inside that configurator (`25:44:.../VaultConfigurator.sol`) the vault, delegator and slasher are all created **and** `Vault.setDelegator()` / `setSlasher()` are invoked in-tx.  
   ⇒ Any later external call to `setDelegator()` can never happen in production.

2. Scan for live call-sites  
   `grep -R "setDelegator("` across `script/**`, `src/**`, and `test/**` shows **only** the configurator (plus PoC tests).  
   No call-site = extremely unlikely to be exercised on-chain.

3. Focus on what **can** be reached post-deployment  
   • Public / external functions that remain callable by anyone (no role, no `onlyOwner`).  
   • Upgrade hooks, withdraw, slash, delegate, reward-claim – anything affecting value or control.  
   • Cross-chain / oracle entrypoints.

4. Check single-use initialisers  
   Functions gated only by “`if (!isXInitialized)`” are high-risk **only** when they are meant to be called later.  
   Verify via step 1 & 2 whether they ever remain unset after the deployment tx. If not, skip.

5. Use code coverage as *signal*, not truth  
   • Un-tested **and** reachable ⇒ top-priority fuzz / unit target.  
   • Un-tested **but unreachable** (like `setDelegator` here) ⇒ low impact unless deployment pattern changes.

Quick checklist for every repo  
[ ] Read deploy scripts/factories → list functions invoked at deploy.  
[ ] Grep repo for each public/external func → confirm live call-sites.  
[ ] Prioritise functions that: handle funds, grant roles, call external contracts, or can be called by anyone after T₀.  
[ ] De-prioritise internal/helpers or one-shot initialisers proven to run in deploy tx.

Following this process steers tests toward truly exploitable surfaces and avoids false-positive “dead code” findings.