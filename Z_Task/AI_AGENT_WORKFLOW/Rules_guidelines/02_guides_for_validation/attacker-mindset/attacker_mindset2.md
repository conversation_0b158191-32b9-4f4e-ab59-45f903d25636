# Universal Vulnerability-Hunting Checklist  
_Use this playbook on **any** Solidity/Foundry code-base to maximise real findings and minimise false-positives or out-of-scope (privileged-only) reports._

PHASE 1
---
## 0. Context Gatekeepers  
1. **Read the Bounty / Scope Docs**  
   • Confirm threat-model & out-of-scope classes (e.g. privileged-only bugs).  
   • Flag `previleged_vulnerability.md` and `Z_Task/BOUNTY_OFFICIAL_DOCS/bounty_information.md` -style exemptions early.
2. **Identify Critical Assets**  
   • Where is value stored?  
   • Which contracts can move / unlock that value?
3. **Map Actors & Permissions**  
   • Build a table of roles → allowed functions.  
   • Distinguish **privileged** vs **public** entry points.

> ❗ **If an issue can only be exploited by an already-trusted privileged role it is out-of-scope – stop & note but do not report.**

---
## 1. Automated Baseline  
1. `forge test -vvvv` – green baseline.  
2. Static-analysis (Slither / Echidna quick fuzz).  
3. Grep for `TODO`, `FIXME`, `unsafe`, inline assembly, `delegatecall`.

Record all tool alerts but manually triage later.

---
## 2. Parameter & Flag Change Fuzzing  
1. Enumerate every `set*`, `enable*`, `disable*`, `update*` function.  
2. Fuzz sequences: `disable → use → enable`, `set(X) → use → set(Y)`.
3. For each state-transition assert invariants still hold (no unauthorised access, maths within bounds, arrays not over-grown).

---
## 3. Symmetry & Cleanup Verification  
| Action Function | Expected Inverse | Check |
|---|---|---|
| `register*` | `unregister*` | Do all storage writes have matching deletes? |
| `add*` | `remove*` | Length/containment symmetrical? |

Automate with grep diff; missing inverse = high-risk candidate.

---
## 4. Unbounded Collection DoS Scan  
For every function that _returns_ a dynamic array:  
1. Identify who controls the length.  
2. If **anyone** can append indefinitely → DoS potential.  
3. Require pagination (`offset`,`limit`) or explicit cap.

---
## 5. Calldata Edge-Case Tests  
1. Any helper that extracts selector (`bytes4 selector = …`) → run with `data.length` of 0,1,2,3.  
2. Ensure graceful handling or explicit revert **before** user gas spikes.

---
## 6. Global-Parameter Dependency Audit  
1. List each mutable global (e.g. `epochDuration`, `minDelay`).  
2. Grep every file for that variable.  
3. If downstream module _caches_ or uses constant copy, update-path may break.

Fix: either make parameter immutable or broadcast change.

---
## 7. Precompile & Chain-Variance Review  
1. Detect hard-coded gas stipends in `staticcall` to precompiles.  
2. Cross-check against at least one L2 (Arbitrum, zkSync, etc.).  
3. Make gas configurable.

---
## 8. Validator / Quorum Edge Cases  
1. Generate validator arrays with: duplicates, `(0,0)` pubkeys, voting power 0, and single signer < quorum.  
2. Run through on-chain verify and any off-chain circuits.  
3. Assert security properties (cannot forge header, cannot bypass quorum).

---
## 9. False-Positive / Privileged Filter  
Before drafting a report, answer:
1. Can _any_ untrusted wallet trigger the bug?  
2. Is an economically-rational attacker incentivised?  
3. Does exploit bypass only-Owner / onlyRole protections?  
4. Is it _not_ disallowed by bounty exclusion list?

If **any** answer is _no_, classify as FP or out-of-scope.

---
## 10. Proof-of-Concept Mandate  
1. Write a minimal Foundry test that fails on vulnerable commit & passes on fixed commit.  
2. Use realistic mainnet fork or mocked economic scenario.  
3. Quantify impact (funds lost, DoS duration, takeover probability).

---
## 11. Report Template Hook  
Link your PoC + fix suggestion into `template_sherlock.md` style report, including:  
• Title  
• Severity justification (impact × likelihood)  
• Step-by-step exploit  
• Recommended mitigation.

---
## Usage Workflow (Quick-Start)
```bash
# 1. baseline
forge test -vvvv

# 2. run auto-scripts (assume you saved them)
./scripts/symmetry_check.sh
./scripts/flag_fuzz.sh
./scripts/unbounded_scan.sh

# 3. review generated logs & craft PoCs

# 4. confirm non-privileged & real impact

# 5. write report
```

PHASE 2
Now, that we know the overview of what the protocol does and have an eagle-eye view of the contracts. Let's dive deep into the contracts and start Manual Auditing. Start Reading the codes Line by Line to get an understanding of the contracts.

Think like an attacker and identify potential places where things can go wrong. The following are some of the most common checks I look for:

Common Smart contract bugs
Access Controls Checks in Critical Functions
Check if the contract complies with the standards
The flow of Function calls
Examine all User-controlled Input
Realistic attack scenarios and edge cases

Follow this checklist line-by-line; if every step passes you dramatically cut the chance of missing medium-plus severity bugs while eliminating noisy false-positives.