# Smart Contract Vulnerability Hunting – System‑Thinking Playbook

**Goal:** Equip auditors (human or AI) with a step‑by‑step mental model and checklist for detecting and proving *any* Ethereum smart‑contract bug while avoiding false positives. The most important thing to put in mind when auditing " So much for “reentrancy is a solved problem.” Audit every state‑changing external call, check your mutexes, and keep those re‑entrancy guards tight and this should be thesame on any state-change, not only re-entrancy, make you verify it does what is ask of it and also check for any accoutning error "

---

## 1  Specification First

1. **Harvest Requirements** – Read all docs, READMEs, deployment scripts, and tests to list intended invariants, roles, and workflows.
2. **Build a Diff** – Compare intended vs. implemented behaviour; every mismatch is a bug candidate.

---

## 2  Threat‑Modelling Lenses

| Lens              | Guiding Question                                      |
| ----------------- | ----------------------------------------------------- |
| **Adversarial**   | If I only control an EO‑A, how can I maximise profit? |
| **State‑Machine** | Which transition breaks an invariant?                 |
| **Economic**      | Is the attack profitable after gas, MEV, slippage?    |

---

## 3  Storage‑Level Attacks

* Generate storage map using any tool or maybe using foundry.
* Hunt for **uninitialised slots**, **packing collisions**, **\_\_gap** misuse.
* Exploit pattern: `delegatecall` that overwrites privileged storage (e.g., `owner`).

---

## 4  ABI & Low‑Level Call Attacks

* Craft calldata via `abi.encodeWithSelector` or raw hex.
* Tweak dynamic‑type offsets/lengths to trigger OOB reads or logic inversion.
* Use `target.call{value:…}(data)` to bypass type safety. Always inspect `(success, data)`.

---

## 5  Invariant Verification

```bash
forge test --match-contract Invariant* -vvvv
```

1. Define protocol‑wide properties (`sum(balances) == totalSupply`).
2. Fuzz with realistic handlers; integrate latest symbolic tools use foundry tools where neccessary.

---

## 6  Classic Bug Classes & Quick Heuristics

| Class               | Heuristic                                      |
| ------------------- | ---------------------------------------------- |
| Reentrancy          | External call before state update, no guard.   |
| Access Control      | Roles not refreshed on upgrade / NFT transfer. |
| Math & Precision    | Division before multiplication ⇒ truncation.   |
| Oracle Manipulation | Single feed, long heartbeat.                   |
| Gas DoS             | Unbounded loop over user‑controlled data.      |

---

## 7  Cross‑Module & Economic Exploits

1. **Module Desync** – Vault updated, Strategy not.
2. **Fee Loops** – Deposit/withdraw cycles extract rewards.
3. **Privilege Chaining** – Harmless fn in A dangerous via proxy B.

---

## 8  PoC Checklist

* Read all on‑chain state; no hard‑coded magic.
* ≤10 tx on the testnet(or mainnet‑fork).
* Log pre/post balances & critical storage.
* Compute `profit – gas ≥ 0`.
* Cross‑validate with invariant test & manual trace.

---

## 9  Standard Report Template

1. **Title & Severity**
2. **Summary**
3. **Root Cause** (file\:line)
4. **Attack Steps** (tx table & state diffs)
5. **Impact (\$ & % TVL)**
6. **Recommended Fix** (code diff)

---

## 10  Quick Reference Checklist

* [ ] Docs/tests reviewed for invariants
* [ ] Storage layout mapped
* [ ] Public funcs fuzzed & invariants tested
* [ ] Oracle & MEV scenarios simulated
* [ ] Upgrade paths & role transfers checked
* [ ] Economic viability proven

 ## invariant test

1. **Global Dependency Map**  
   Build (scripted) map of each mutable global parameter → modules that *cache* or *assume* it. Alert when dependency count >0.
2. **State-Transition Fuzzing**  
   For every feature flag (`bool`) or role-gated mode: fuzz sequences of `disable → register → enable` etc. Track invariants.
3. **Symmetry Checker**  
   Auto-grep for mappings modified in *register* functions and assert a corresponding delete/update in *unregister* functions.
4. **Edge-Calldata Tests**  
   Automatically test helper functions that slice `data` with cases `0–3` bytes to catch selector underflows.
5. **Unbounded Collection Scanner**  
   Detect public getters that return dynamic arrays whose length can be increased by anyone; flag for pagination.
6. **Chain Compatibility Audit**  
   List every explicit gas stipend or precompile constant; cross-check against L2 chain configs.
7. **Validator-Set Adversarial Suite**  
   Generate validator arrays containing duplicates, `(0,0)` keys, and single-signer cases; run through circuit & Solidity verifiers.
8. **Continuous Proof-of-Concept Harness**  
   Each heuristic above has an associated Foundry test template autogen script to validate exploits during audit.

By integrating these automated and mental checks into the existing 10-step audit ladder, the next engagement should surface the seven missed issues (and similar classes) early.
