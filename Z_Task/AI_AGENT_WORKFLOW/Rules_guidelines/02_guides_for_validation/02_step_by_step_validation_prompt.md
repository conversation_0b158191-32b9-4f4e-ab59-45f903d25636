# AI Agent Workflow: Step-by-Step Vulnerability Validation Prompt

Follow the step by step below to validate a reported vulnerability in this codebase.  The process is deliberately strict: every claim must be backed by direct evidence from code, tests, script, or documentation.  No assumptions, no speculation, no lies.

---

## 0. Invocation Parameters  
Absolute path to the markdown report you are to validate.  
Report_path: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Asymmetry/Z_Task/manual_analysis/withdrawal_dos_min_deposit.md `


---

## 1. Intake & Report Parsing
1. Open **<Report_path>** and extract:  
   • Alleged vulnerable function(s) & contract name(s).  
   • Attack scenario & claimed impact.  
   • Severity classification given by reporter.  
2. Pause and double-check that nothing was missed.

---

## 2. Protocol & Bounty Context
1. Read the official docs:
   - `Z_Task/BOUNTY_OFFICIAL_DOCS/bounty_docs.md`
   - `Z_Task/BOUNTY_OFFICIAL_DOCS/bounty_information.md`
2. Identify:
   • Protocol goals & economic model.  
   • In-scope / out-of-scope components.  
   • Accepted vulnerability classes & severity rubric.
3. Cross-reference the report's claims with this context.  Note any mismatches.

---

## 3. Codebase Reconnaissance
1. Locate the alleged function(s) in source:
   • Use filename & symbol search across `contracts/`, `src/`, and `deploy/`.  
2. Open the implementation file(s) **in full**, including inherited contracts, modifiers, and libraries.  
3. Open the **deployment script(s)** for these contracts (search in `script/` & `deploy/`).  Map mainnet addresses and constructor parameters.  
4. Draw a dependency graph: which other contracts can call or be called by the target?
5. **Pause** – ensure a complete mental model before proceeding.

---

## 4. Historical & Pattern Comparison
1. Read `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Asymmetry/Z_Task/vuln_detectors/AI_Vulnerability_Pattern_Recognition_System.md`.  
2. Compare the current claim to past confirmed vulnerabilities of other protocols.  
3. Note any pattern matches or red flags that strengthen / weaken credibility.

---

## 5. Manual Validation Checklist
Apply each item; record evidence (file & line numbers):
1. Business-logic consistency with docs.  
2. Access control & privilege boundaries.  
3. Re-entrancy & external call ordering.  
4. Math / precision errors.  
5. Oracle & price manipulation vectors.  
6. Gas griefing / DoS potential.  
7. Upgradeability & proxy quirks.  
   • If the claim is about a "public initializer" or "two-step proxy upgrade":
     1. Fetch production deployment script **or** decode mainnet transactions for the proxy.
     2. Accept only if you see *two separate txs* (`upgradeTo` **then** `initialize`).  
     3. Otherwise mark as **Unverifiable → Out of Scope**.
  
8. Cross-contract interaction assumptions. etc  

---

## 6. Severity & Impact Assessment
If the bug is real:
1. Calculate severity using bounty rubric (high, medium, low).  
2. Quantify realistic financial impact and likelihood.  
3. Reference concrete numbers (e.g., TVL, role counts).

---

## 7. Draft Validation Report
Produce a concise markdown file in `Z_Task/manual_analysis/validated/`:
- **Status**: Confirmed / Rejected.  
- **Summary** (≤ 5 lines).  
- **Evidence**: a step by step of how the vulnerability is and its root.  
- **Severity & Impact** (with rubric citation).  
- **Suggested Fix / Mitigation**.  

---

## 8. Final Sanity Pass
1. Re-read your own report & all cited files.  
2. Ensure no assumption or unverified statement remains.  
3. Commit artifacts.  
4. **Stop.**

---

### Meta-Rules for the AI Agent
- Work **sequentially**; do not skip sections.  
- After each major section, pause and self-verify before continuing.  
- **Never** invent information; if uncertain, mark as "Unknown" and investigate.  
- Keep answers ultra-concise and strictly factual.  
- Cite file locations in the form `startLine:endLine:path/to/file`.