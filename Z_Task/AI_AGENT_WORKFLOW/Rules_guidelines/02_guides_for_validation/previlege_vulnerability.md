Below is a quick-reference checklist you can use before spending time on a finding.  It separates “privilege-dependent” issues that most bug-bounty programs accept from those they usually mark **out-of-scope**.

ACCEPTED (privilege **mis-use / escalation / bypass**)  
• Anyone-can-be-admin: missing or weak access-control lets an unprivileged EOA obtain `owner`, `DEFAULT_ADMIN_ROLE`, proxy-upgrade keys, etc.  
• Single-sig upgrade / emergency functions that are documented as multi-sig or timelock-gated.  
• Privileged function callable through an unexpected route (e.g., delegatecall / proxy selector-clash).  
• Role-check order bug (`require(x); … _grantRole(x);`) that lets an attacker grant themselves privileges.  
• Logic that temporarily grants excessive rights (e.g., flash-loaned governance tokens, snapshot manipulations).  
• Contract initialisation that can be front-run so the attacker sets themselves as owner.  
• Upgrade beacon/proxy pointing to attacker-controlled implementation (privilege misuse with permanent impact).  
• Re-entrancy or unchecked-call that lets an attacker skip an only-owner modifier mid-execution.  
• “Shadow” owner variables (two owner states, one controllable by attacker).

TYPICALLY REJECTED (requires **intended** privileges)  
• `onlyOwner` can drain funds or change parameters—explicitly given, well-documented powers.  
• Multisig / DAO can set fee = 100 %, pause system, mint infinite tokens, etc., if that is an acknowledged governance right.  
• Pauser role can halt the protocol (that’s the point of the role).  
• Admin can upgrade proxy to malicious code when upgradeability is disclosed and expected.  
• Timelock can execute any queued transaction after the delay.  
• Guardian can slash, seize, veto, or otherwise act as described in docs.  
• “Centralisation risk” with no concrete bug (e.g., single EOA owns proxy) unless the program explicitly lists it as in-scope.

GREY AREA – check program rules or ask triage before investing time  
• Privileged function protected only by a **weak** multisig (e.g., 1-of-2 on a multi-billion TVL).  
• Admin key held in upgradeable contract that could be self-destructed.  
• Functions callable by cross-chain bridge or oracle that might be compromised—programs differ on whether this is “external dependency” or “privileged role.”

Practical filter before reporting  
1. Can an attacker with **no more authority than any normal user** trigger the bug? If yes → in scope.  
2. Does the bug **grant** the attacker a higher role (privilege escalation)? If yes → in scope.  
3. Does the attack merely use powers the contract explicitly gives its owner / governance? If yes → usually out of scope.  
