# STEP 1

## Web3 Function Vulnerability Analysis Prompt

### Analysis Instructions

You are a Web3 security researcher analyzing a smart contract function. Perform a comprehensive vulnerability assessment following these steps:

#### 1. Access Control Analysis
- **Role Verification**: Check if actors have appropriate roles (admin of fund A ≠ admin of fund B)
- **Permission Boundaries**: Verify users cannot exceed their designated access levels
- **Cross-Contract Permissions**: Ensure no unauthorized cross-contract admin privileges

#### 2. Modifier Validation
- **Modifier Completeness**: Verify each modifier fully satisfies its security intent
- **Sandwich Attack Vectors**: Check if admin settings can be sandwiched or exploited during state transitions
- **Modifier Bypass**: Look for ways to circumvent modifier restrictions

#### 3. Input & State Validation
- **Loop-Based Bypasses**: Focus on functions with loops returning true/false - check for validation bypasses
- **State Consistency**: Verify state changes maintain system integrity
- **Input Sanitization**: Check for malformed input exploitation

#### 4. Critical State Function Analysis
For withdraw/deposit, mint/burn, repay, claim functions:
- **State Update Mapping**: Document what variables are updated and how
- **Execution Flow**: Trace complete execution path for manipulation points
- **Attack Surface**: Identify where attackers can inject malicious behavior

#### 5. Storage & Symmetry Issues
- **Unused Storage**: Flag storage variables written but never read (indicates missing logic)
- **Bad Symmetry**: Check for copy-paste errors causing hash collisions or logic conflicts
- **Defense Balance**: Verify defenses don't create DoS conditions

#### 6. Upstream/Downstream Verification
- **Upstream Checks**: Analyze calling functions for protective mechanisms
- **Downstream Validation**: Trace function calls for security validations
- **Complete Path Analysis**: Map full execution flow to verify exploitability

STEP 2
# Web3 Vulnerability Validation Checklist

## Final Validation Steps

Before reporting any vulnerability, perform comprehensive validation:

#### 1. Upstream Analysis
- Check calling functions for access controls
- Verify input validation and sanitization
- Look for modifier restrictions (onlyOwner, whenNotPaused, etc.)

#### 2. Downstream Analysis  
- Trace function calls to identify protective checks
- Review state changes and their validations
- Check for circuit breakers or emergency stops

#### 3. Context Verification
- Examine the complete execution path
- Verify exploitability under realistic conditions
- Test edge cases and boundary conditions

#### 4. Final Validation
- Confirm the vulnerability is genuinely exploitable
- Document the complete attack vector
- Verify impact assessment is accurat

### Output Format
```
FUNCTION: [function_name]
VULNERABILITY: [type] - [severity]
ATTACK VECTOR: [detailed exploitation path]
UPSTREAM CHECKS: [protective mechanisms found/missing]
DOWNSTREAM VALIDATION: [security validations present/absent]
EXPLOITABILITY: [confirmed/false-positive with reasoning]
```

## Critical Reminder
**Only report confirmed vulnerabilities after complete upstream/downstream validation. No assumptions - verify every step.**

