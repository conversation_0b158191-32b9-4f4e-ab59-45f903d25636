Efficient Vulnerability-Hunting Workflow

Focus your hunt on protocol design flaws that directly harm users or developers using the sdk. Ignore user error and intended centralization features unless they introduce an exploitable risk, if the vulnerability is previleged ones, make sure the vulnerability is within the type of accepted vulnerability as described in `/Z_Task/AI_AGENT_WORKFLOW/Rules_guidelines/02_guides_for_validation/previlege_vulnerability.md` otherwise dont report the previleged vulnerability . in the report include the severity, impact and likelohood level of potential vulnerability found. Rememer you are to run this pattern step by step, you omly stop when you found vulnerability to report, dont break this rule.

1. Read `Z_Task/BOUNTY_OFFICIAL_DOCS/bounty_docs.md` to grasp protocol goals, actors, and assumptions.  
2. Explore the entire codebase until you clearly understand every moving part and its economic logic, get more clues about vulnerabilites here `/Z_Task/vuln_detectors/AI_Vulnerability_Pattern_Recognition_System.md` that you could use later for pattern recognition and more.

3. Focus on the function provided and the related deployment script; know exactly what the function must guarantee.  
4. Apply the `Z_Task/AI_AGENT_WORKFLOW/SYSTEM/08_My_BRAIN.md` and `/Z_Task/AI_AGENT_WORKFLOW/Rules_guidelines/02_guides_for_validation/attacker_mindset.md` to that function.  
   • If you spot a medium-, high-, or critical issue → go to step 8.  
5. If nothing surfaces, run the `“Z_Task/AI_AGENT_WORKFLOW/SYSTEM/ADVANCED_VULNERABILITY_HUNTER_PROMPT.md”`. 
6. Still nothing ?, try this `Z_Task/AI_AGENT_WORKFLOW/Rules_guidelines/MYRULES.MD ` make sure you follow this line by line.
7. Still nothing? Run the `“/Z_Task/AI_AGENT_WORKFLOW/SYSTEM/INTEGRATED_SECURITY_RESEARCH_SYSTEM.md”`.  
8. If needed, perform a line-by-line manual review via `Z_Task/AI_AGENT_WORKFLOW/SYSTEM/04_MANUAL_FUNCTION_ANALYSIS.md`.  
9. When you identify a bug, draft a report in `Z_Task/manual_analysis` using the `/Z_Task/manual_analysis/01_Settlement_Genesis_Overwrite.md` template.  
10. Verify each finding with iteration or proofs AND Before that, get more clues about vulnerabilites here `Z_Task/vuln_detectors/vuln_categories.md` that you could use for pattern recognition and how to understand a real vulnerability from false-positive before submitting; no assumptions, no guesses, only demonstrable facts.

Always pause after each major step to double-check your conclusions.

---
### Guard Against False "Un-initialized Proxy" Claims
If a reported issue involves a **public `initialize` or two-step proxy upgrade pattern**, the reviewer **MUST**:
1. Retrieve the *production* deployment script **or** decode on-chain transactions (BaseScan / Etherscan) for the proxy address.
2. Confirm that **two distinct calls** were made in production:
   - `upgradeTo(impl)` *(or similar)*, **and later**
   - `initialize(...)`.
3. Acceptable proof = deployment JSON or mainnet tx-hash pair.  
   • Unit-tests or local scripts are **not** sufficient.
4. If such evidence is absent, classify the finding as **Unverifiable → Out of Scope** and stop the report.


continue, you should also have it in  mind that this is a AN L2 AND L1 CONTRACTS of COINBASE, am sure this will help in understanding vulnerability