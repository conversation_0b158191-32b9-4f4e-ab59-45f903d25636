# Rapid Validation Checklist

Use this micro-workflow when you receive a **single vulnerability report + PoC test**.  Work through the steps in order; do **not** skip any item.

1. Deployment Reality Check  
   • Open the *relevant deployment script(s)* ⇒ confirm which contract + parameters are currently used in production.  
   • Note any discrepancies with the contract under review.
   

2. Spec & Intent  
   • Read `Z_Task/BOUNTY_OFFICIAL_DOCS/bounty_docs.md` and any inline code comments to understand the intended behaviour.

3. Scope Gate  
   • Read `Z_Task/BOUNTY_OFFICIAL_DOCS/bounty_information.md` to verify the issue is in-scope.  
   • Decide if the bug is **privileged**; if yes, ensure it belongs to an accepted privileged class, otherwise stop.
   . If you see a sensitive function or critical function without protection, check the official doc and comment to verify wether it is suppose to be previleged or non-previleged function or feature.
   

4. Internal Security Signals  
   • Open the contract's own test file(s) to see the enforced invariants and assumed checks.

5. PoC Alignment  
   • Run through the PoC test `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Asymmetry/USDaf-v2/contracts/test/vulnerabilities/UrgentRedemption_UnbackedDebt.t.sol `.  
   • Follow emitted logs – confirm they hit the allegedly vulnerable code or not.
   . Make sure there is real interaction between the demonstrated test and the vulnerable code
   . make sure input data or something is not manipulated that could yield false-positive results
   . Make sure there is no use of mocks that could yield a false-positive result

6. Report Consistency  
   • Read `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Asymmetry/Z_Task/REPORT/UrgentRedemption_Unbacked_Debt_validated.md `.  
   • Ensure every claim is backed by the PoC behaviour and exact code locations – no gaps, no assumptions.

7. Pattern & Severity Sanity  
   • Cross-check with `Z_Task/vuln_detectors/AI_Vulnerability_Pattern_Recognition_System.md` to see if the finding fits validated vulnerability patterns.  
   • Re-evaluate economic, mathematical, logical and practical impact; remove any exaggeration.

8. Before Verdict
   Before giving your final verdict, Ask yourself " Is it used anywhere in the part of a contract or in the deployment, like does it interact with any sensitive code that is critical to the protocol security, is there a protection or requirement that could make this vulnerability ineffective or invalid?, does the log traces function flow show any sign of weakness to the demonstration that could make it false-positive demonstration, is there real interaction between the attack and the vulnerable code, does the attack path manipulate some requirement or protection that in production, i mean mainnet will be infeasible, make sure everything is genuine, practical, realistical and verifiable, no assumption or lie should be made anywhere both in test and in the report."

9. Before anything else, read and understand this file to know wether the vulnerability is valid or false-positive `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/coinbase/Task/AI_AGENT_WORKFLOW/Rules_guidelines/guides_for_validation/target_areas_to_vuln.md`, very important, you must read it

10. Verdict  
   • If **all** checks pass →  conclude with **VALID**.  
   • Else → conclude the mismatch and mark as **REJECTED/NEEDS-REVISION**.

---
Keep comments terse, cite file locations as `line:start-end:path`, and avoid speculative language. 