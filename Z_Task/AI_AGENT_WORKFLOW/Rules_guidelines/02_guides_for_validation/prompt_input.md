STEP 1
When you are analyzing this function `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/LIDO/Z_Task/AI_AGENT_WORKFLOW/01_list_of_critical_functions/13_timelock_deactivateEmergencyMode.md`, use this attacker mindset `Z_Task/AI_AGENT_WORKFLOW/Rules_guidelines/02_guides_for_validation/attacker_mindset.md` and this `Z_Task/AI_AGENT_WORKFLOW/SYSTEM/04_MANUAL_FUNCTION_ANALYSIS.md` then check here deeply /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/LIDO/Z_Task/vuln_detectors/vuln_categories.md and /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/LIDO/Z_Task/vuln_detectors/AI_Vulnerability_Pattern_Recognition_System.md if you could find the type of vulnerabilities listed in the function,  , and must have read the doc `Z_Task/BOUNTY_OFFICIAL_DOCS/bounty_docs.md` to understand the spec very well, find a way to invalidate its properties using invariant test(not creating test files, by using python calculation in the background to check), dont skip any part, follow each part using chain of thought, dont assume or fake anything, verify everything. please analyze deeply

 STEP 2
 if you think the vulnerability is valid, before you report it, make sure it satisfy this `Z_Task/AI_AGENT_WORKFLOW/Rules_guidelines/02_guides_for_validation/05_removing_false_positive.md` and this `/Z_Task/AI_AGENT_WORKFLOW/Rules_guidelines/02_guides_for_validation/06_down&upstream_check.md`, and is satisfy this too `Z_Task/BOUNTY_OFFICIAL_DOCS/known_risk.md`  before you go to this step `/Z_Task/AI_AGENT_WORKFLOW/Rules_guidelines/02_guides_for_validation/03_validate_and_POC.md`,  thats how to use the attacker's mindset, it helps you in trying different ways like edge cases, formal verification, fuzzing etc, you can try those tools when necessary to prove the invalidation of a property, dont skip any step, follow and craft it like to-do, and dont stop until you reach the last task in the to-do

 try this approach then `Z_Task/AI_AGENT_WORKFLOW/Rules_guidelines/02_guides_for_validation/attacker-mindset/attacker_mindset2.md`, dont assume, verify

 follow this step by step to validate this vulnerability `Z_Task/AI_AGENT_WORKFLOW/Rules_guidelines/02_guides_for_validation/attacker-mindset/attacker_mindset2.md`, dont assume, verify


START THIS TASK  by first reading and understanding this function `/Z_Task/AI_AGENT_WORKFLOW/Rules_guidelines/02_guides_for_validation/previlege_vulnerability.md`, then follow this step by step approach `Z_Task/AI_AGENT_WORKFLOW/Rules_guidelines/02_guides_for_validation/01_Instruction.md` make sure it satisfy this `Z_Task/AI_AGENT_WORKFLOW/Rules_guidelines/02_guides_for_validation/05_removing_false_positive.md`, this `/Z_Task/AI_AGENT_WORKFLOW/Rules_guidelines/02_guides_for_validation/06_down&upstream_check.md` and this `/Z_Task/AI_AGENT_WORKFLOW/Rules_guidelines/02_guides_for_validation/previlege_vulnerability.md`  , DONT SKIP ANY PART or STEP, NO MATTER HOW SMALL OR BIG IT IS, please no pause until you reach to the last part of the task, use a doto step by step and dont stop until you reach the last task in the to-do


FOR TESTED FUNCTION :
follow this step by step `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Asymmetry/Z_Task/AI_AGENT_WORKFLOW/Rules_guidelines/01_guides_for_validation/02_finding_vuln_on_tested.md` for this `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Asymmetry/Z_Task/AI_AGENT_WORKFLOW/02_tested_critical_function/completed/ActivePool_getNewApproxAvgInterestRateFromTroveChange.md`, dont assume, verify everything, once you confirm there could be vulnerability, write a report in `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Asymmetry/Z_Task/manual_analysis`, using this `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Asymmetry/Z_Task/manual_analysis/01_Stability_Pool_Pending_Yield_Capture.md` as template