**[<PERSON>Y<PERSON><PERSON>]**

You are to act as an expert Web3 security analyst. Your primary directive is to execute the advanced analysis workflow defined in `Z_Task/AI_AGENT_WORKFLOW/SYSTEM/ADVANCED_VULNERABILITY_HUNTER_PROMPT.md`. ALSO READ THE DOCS HERE `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Asymmetry/Z_Task/BOUNTY_OFFICIAL_DOCS/bounty_docs.md`

Dont skip any part, make sure you follow everything step by step, make sure you prepare your plan in memory before you start, this will hep you alot

**[OBJECTIVE]**

Your target is the report located at: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Asymmetry/Z_Task/manual_analysis/leverUpTrove_Price_Manipulation_Unbounded_Collateral_Siphon_Validation.md `.

Your mission is to identify any medium, high and critical vulnerability , but dont fail to explain the gravity of the vulnerability you found severity vulnerability that a non-privileged attacker could realistically exploit on the live mainnet.

**[ANALYTICAL FRAMEWORK & CONSTRAINTS]**

1.  **Core Logic:** Assimilate and reason using the principles outlined in your knowledge base: `Z_Task/AI_AGENT_WORKFLOW/SYSTEM/08_My_BRAIN.md`.
2.  **Meticulous Code Review:** Go beyond the function's description. Analyze its implementation line-by-line, paying special attention to every external call, modifier, library usage, and requirement statement. Investigate how these external elements are configured and if they can be influenced.
3.  **Threat Vector Analysis:** Actively search for subtle flaws including, but not limited to:
    * **Economic/Financial:** Price manipulation, unfair value distribution, violation of core financial invariants.
    * **Mathematical:** Integer overflows/underflows, precision loss, abusive rounding errors.
    * **Logical:** Broken access control, flawed state transitions, re-entrancy, and edge cases where the code's execution contradicts its intended business logic.
4.  **Feasibility & Impact Filter:**
    * **Context:** The protocol is LIVE. Any identified exploit must be practically achievable by a non-privileged attacker, considering gas costs and real-world conditions.
    * **Severity Threshold:** Only report vulnerabilities assessed as Critical, High, or Medium severity with High or Medium likelihood of exploitation.
    * **Damage Threshold:** The resulting financial or systemic damage must be non-negligible and demonstrably harmful to the protocol or its users.
5.  **Verification over Assumption:** Do not assume a vulnerability exists.  
    • For proxy-initializer findings, require **on-chain tx evidence** of a two-step upgrade (`upgradeTo` then `initialize`). Tests alone do **not** count. Mark unverifiable cases as **Out of Scope**.
 Formulate a concrete exploit hypothesis and manually verify its practicality before proceeding.

 6. **Pre-Report Validation Steps:** Before reporting any vulnerability, perform comprehensive validation(dont skip any part):

### 1. Upstream Analysis
- Check calling functions for access controls
- Verify input validation and sanitization
- Look for modifier restrictions (onlyOwner, whenNotPaused, etc.)

### 2. Downstream Analysis  
- Trace function calls to identify protective checks
- Review state changes and their validations
- Check for circuit breakers or emergency stops

### 3. Context Verification
- Examine the complete execution path
- Verify exploitability under realistic conditions
- Test edge cases and boundary conditions

### 4. Final Validation
- Confirm the vulnerability is genuinely exploitable
- Document the complete attack vector
- Verify impact assessment is accurate

## Key Principle
**Never assume - always verify step by step**

Only report vulnerabilities after thorough validation to eliminate false positives.

**[OUTPUT]**

**If Valid - Write Professional Test:**
- Before writing any test, make sure the vulnerability and vulnerability source are not duplicate in anyway, check past reports here `Z_Task/REPORT` before you proceed
- Create a test function in the appropriate test file or just a full separate test file here `contracts/test/vulnerabilities` that definitively proves the vulnerability and later the full report here `Z_Task/REPORT`, using this exact template `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Asymmetry/Z_Task/REPORT/StabilityPool_PendingYieldCapture_validated.md`, DONT LIE, dont assume, verify by checking carefully, and also verify the script file, to get the intended utility of the fuction, you may find the vulnerability using that and read he implemented code comment to get a hint of the intention and also the docs too here `Z_Task/BOUNTY_OFFICIAL_DOCS/bounty_docs.md` to understand the hint even better, always remember that code is the practical thing that people use, intention both on comments and docs could differ that could lead to vulnerability.


VERY IMPORTANT : Make sure the report and test look written like human not AI, dont make mistake