# Rules – Invariant-Driven Analysis

Use these rules whenever you run `07_invariant_driven_analysis_prompt.md`.

1. **Invariant First** — Never start from single functions; begin from global safety properties.
2. **Non-Privileged** — Ignore attacks that require roles with `owner`, `guardian`, `pauser`, or timelock authority.
3. **Two-Tx Budget** — If the exploit needs more than two external calls, discard (too complex to be realistic).
4. **Duplicate Filter** — Before reporting, compute semantic similarity (>0.8 cosine) against historical audits/bugs; drop near-matches.
5. **Economic Viability** — Estimate cost vs. profit. Negative or trivial EV (<0.5 ETH profit) = reject.
6. **Assumption Transparency** — List every assumption. If any assumption seems brittle (oracle delay, gas refund, etc.) flag it; unclear assumptions mean rejection.
7. **Invariant Tests Mandatory** — Provide a Foundry invariant test for each candidate; reports without a failing test are ignored.
8. **Solidity 0.8.24+** — All snippets & tests must compile under the repo’s configured compiler version.
9. **No Partial State Edits** — Invariant tests must leave chain in a realistic state; revert or roll back if exploit bricked the contract.
10. **Clear JSON Output** — Use the exact JSON schema in the prompt. The pipeline auto-parses it. 