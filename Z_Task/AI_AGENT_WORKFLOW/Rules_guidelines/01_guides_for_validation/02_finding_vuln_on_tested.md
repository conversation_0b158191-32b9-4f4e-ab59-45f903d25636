# Solidity Security Test Generation & Vulnerability Analysis

**Task**: Analyze function implementations against existing tests to identify security gaps and generate comprehensive attack vectors.

## Analysis Framework

### 1. Coverage Gap Analysis
- **Compare**: Existing test coverage vs. complete function logic
- **Identify**: 
  - Uncovered code branches
  - Missing boundary conditions
  - Unvalidated state transitions
  - Inadequate input sanitization etc

### 2. Edge Case Discovery
- **Boundary Values**: Zero, max uint, overflow/underflow points
- **State Combinations**: Invalid states, race conditions, reentrancy paths
- **Input Variations**: Malformed data, unexpected types, extreme values
- **Time Dependencies**: Block timestamp manipulation, deadline bypasses

### 3. Assumption Challenge Matrix
- **Access Control**: Can non-privileged users bypass restrictions?
- **State Invariants**: What breaks the expected system state?
- **Economic Logic**: Can value be extracted/manipulated?
- **External Dependencies**: Oracle manipulation, external contract failures

## Vulnerability Hunt Checklist

### Critical Areas to Test:
- [ ] **Arithmetic Operations**: Overflow, underflow, division by zero
- [ ] **Access Control**: Role bypasses, signature replay, authorization gaps
- [ ] **State Manipulation**: Reentrancy, front-running, MEV extraction
- [ ] **Input Validation**: Malformed calldata, unexpected msg.value
- [ ] **External Calls**: Failed calls, return value manipulation
- [ ] **Gas Limits**: DoS via gas consumption, out-of-gas scenarios

## Test Generation Protocol

### For Each Untested/Weak Area:

```solidity
function testExploit_[VulnerabilityType]_[FunctionName]() public {
    // Setup: Create vulnerable state
    // Attack: Execute exploit vector
    // Assert: Verify unexpected behavior/damage
    // Impact: Document potential loss/compromise
}
```

### Test Categories:
1. **Privilege Escalation Tests**: Non-admin performing admin functions
2. **Economic Exploit Tests**: Value extraction, fund drainage
3. **State Corruption Tests**: Breaking contract invariants
4. **Denial of Service Tests**: Blocking legitimate operations
5. **Integration Attack Tests**: Multi-contract attack vectors

## Output Requirements

### Generate:
- **Exploit Test Suite**: Complete `.t.sol` file with attack scenarios
- **Vulnerability Report**: Detailed findings with severity ratings
- **Mitigation Strategies**: Specific code fixes and recommendations

### Test Documentation Format:
```solidity
/// @notice [CRITICAL/HIGH/MEDIUM/LOW]: [Vulnerability Description]
/// @dev Attack Vector: [How the exploit works]
/// @dev Impact: [Potential damage/loss]
/// @dev Root Cause: [Why this vulnerability exists]
function testExploit_[Name]() public {
    // Test implementation
}
```

## Execution Strategy
1. **Map** all existing test assertions to code lines
2. **Identify** untested branches and weak test coverage
3. **Generate** attack scenarios for each gap
4. **Validate** exploits can actually break the contract
5. **Document** findings with severity and remediation

**Goal**: Uncover vulnerabilities that could lead to fund loss, unauthorized access, or system compromise through systematic adversarial testing.