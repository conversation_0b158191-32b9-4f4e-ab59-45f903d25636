# 🧪 SOLIDITY TEST COVERAGE ANALYSIS WORKFLOW
**AI Agent Task 2: Comprehensive Test Coverage Documentation**

---

## **📋 TASK OVERVIEW**
Your task is to systematically analyze the Foundry test suite, map all tested functions in the `test/` folder and trace their file location in `src/`(this will help in know the full path of the function), identify tested logic areas, and document comprehensive test coverage for security audit purposes and think like a hacker and analyse critical areas not tested that could pose security risk, document them.

## **🎯 PRIMARY OBJECTIVE**
After analyzing all test files in the `test/` folder, extract function calls targeting `src/` contracts, identify tested logic areas and their security implications, and report everything in individual `.md` files in the `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Asymmetry/Z_Task/AI_AGENT_WORKFLOW/02_tested_critical_function/completed` folder.

---

## **📝 STEP-BY-STEP INSTRUCTIONS**

### **Step 1: Environment Setup**
```bash
# Navigate to your workspace
cd into the folder of the codebase

# Ensure Task folder exists
cd Z_Task

# Create the tested functions documentation folder if already exist, skip creating one and use the existing one
mkdir -p Z_Task/AI_AGENT_WORKFLOW/02_tested_critical_function
```

### **Step 2: Test Suite Analysis Strategy**
**🔍 What to Look For:**
- **Test Files**: All `.t.sol` files in `test/` folder
- **Function Calls**: Direct calls to `src/` contract functions
- **Test Scenarios**: Different input combinations and edge cases
- **Assertions**: What specific conditions are being validated
- **Mock Interactions**: How external dependencies are tested
- **State Verification**: What state changes are verified after function calls

**📍 Search Strategy:**
1. Start with `test/` folder - scan all `.t.sol` files
2. Look for contract instantiation from `src/` folder
3. Identify function calls to `src/` contracts
4. Map test methods to specific logic branches
5. Extract assertions and their purposes
6. Note security-critical test areas

### **Step 3: Function Test Documentation Template**
For each tested function, document using this exact format (each function gets its own file):

```markdown
## Function: [CONTRACT_NAME].[FUNCTION_NAME]

### 📍 **Source & Test Locations**
- **Source Path**: `src/contracts/[FILE].sol:[LINE_NUMBER]`
- **Test Path**: `test/[FILE].t.sol:[LINE_NUMBER]`
- **Contract**: [CONTRACT_NAME]
- **Function Signature**: `[FUNCTION_SIGNATURE]`

### 🧪 **Test Coverage Analysis**

#### **Test Method 1: [TEST_FUNCTION_NAME]**
- **Purpose**: [What this test validates]
- **Test Logic**: [Specific logic branch being tested]
- **Security Implication**: [Why this test matters for security]
- **Assertions**: [What conditions are verified]
- **Edge Cases**: [Boundary conditions tested]

#### **Test Method 2: [TEST_FUNCTION_NAME]**
- **Purpose**: [What this test validates]
- **Test Logic**: [Specific logic branch being tested]
- **Security Implication**: [Why this test matters for security]
- **Assertions**: [What conditions are verified]
- **Edge Cases**: [Boundary conditions tested]

[Continue for all test methods...]

### 🔍 **Tested Logic Areas**
- **Area 1**: [Logic Area Name] - [Purpose/Security implication]
  - **Coverage**: [What is tested]
  - **Test Methods**: [List specific test function names]
  - **Assertions**: [Key validations]

- **Area 2**: [Logic Area Name] - [Purpose/Security implication]
  - **Coverage**: [What is tested]
  - **Test Methods**: [List specific test function names]
  - **Assertions**: [Key validations]

### 🚨 **Security-Critical Test Areas**
- **Access Control**: [How permissions are tested]
- **State Changes**: [How state modifications are verified]
- **Input Validation**: [How input sanitization is tested]
- **External Calls**: [How external interactions are tested]
- **Mathematical Operations**: [How calculations are verified]
- **Error Handling**: [How error conditions are tested]

### 📊 **Test Coverage Summary**
- **Total Test Methods**: [NUMBER]
- **Logic Branches Covered**: [LIST]
- **Security Areas Tested**: [LIST]
- **Edge Cases Covered**: [LIST]

### 🔗 **Interface Implementation Cross-Reference**
- **Interface**: [If function implements interface]
- **Implementation Compliance**: [How well tests verify interface compliance]
- **Standard Compliance**: [ERC standards, if applicable]

---
```

### **Step 4: Test Analysis Process**
**📚 Analysis Steps:**
1. **Scan Test Files**: Read all `.t.sol` files systematically
2. **Extract Function Calls**: Identify calls to `src/` contracts
3. **Map Test Methods**: Link test functions to source functions
4. **Analyze Assertions**: Understand what each assertion validates
5. **Identify Coverage Gaps**: Note untested code paths
6. **Document Security Focus**: Highlight security-critical tests

**🔗 Reference Resources:**
- Foundry Testing Guide: https://book.getfoundry.sh/forge/tests
- Solidity Testing Best Practices: https://docs.soliditylang.org/en/v0.8.26/
- Smart Contract Testing Patterns: https://github.com/crytic/building-secure-contracts

---

## **🎯 EXECUTION RULES**

### **✅ DO's:**
- **Start with most critical functions** - financial and access control first
- **Include complete test method analysis** - don't skip any test
- **Document assertion purposes** - why each check matters
- **Map all logic branches** - comprehensive coverage analysis
- **Focus on security implications** - why each test matters for security
- **Cross-reference with source code** - ensure accurate mapping
- **Note untested paths** - identify potential gaps

### **❌ DON'Ts:**
- **Don't analyze source files** - focus only on test coverage
- **Don't skip complex test scenarios** - document everything
- **Don't ignore edge case tests** - they're often most important
- **Don't copy-paste without understanding** - analyze each test
- **Don't miss mock interactions** - they test external dependencies
- **Don't rush the mapping** - accuracy is critical

### **🚨 CRITICAL REQUIREMENTS:**
1. **Complete Test Mapping**: Link every test to source function
2. **Assertion Analysis**: Understand what each assertion validates
3. **Security Focus**: Highlight security-critical test areas
4. **Gap Identification**: Note untested code paths
5. **Individual Files**: Each function gets its own documentation file

---

## **📄 OUTPUT SPECIFICATION**

### **Files Location**: `Z_Task/AI_AGENT_WORKFLOW/02_tested_critical_function`

### **File Naming Convention**:
- `[ContractName]_[FunctionName].md`
- Example: `TokenSwap_swapExactTokensForTokens.md`


## Executive Summary
[Brief overview of total functions tested and coverage quality]

## Tested Functions Inventory
- **Total Functions Analyzed**: [NUMBER]
- **Well-Tested Functions**: [LIST]
- **Partially Tested Functions**: [LIST]
- **Security-Critical Functions**: [LIST]

## Coverage Quality Assessment
- **High Coverage Functions**: [Functions with comprehensive tests]
- **Medium Coverage Functions**: [Functions with adequate tests]
- **Low Coverage Functions**: [Functions with minimal tests]

## Security Test Analysis
- **Access Control Tests**: [Quality and coverage]
- **Financial Function Tests**: [Quality and coverage]
- **State Change Tests**: [Quality and coverage]
- **Error Handling Tests**: [Quality and coverage]

## Identified Gaps
- **Untested Code Paths**: [Critical paths without tests]
- **Missing Edge Cases**: [Important scenarios not covered]
- **Security Test Gaps**: [Security-critical areas undertested or missed]

## Recommendations
- **Priority Test Additions**: [Most important missing tests]
- **Test Quality Improvements**: [How to enhance existing tests]
- **Security Focus Areas**: [Where to add security-focused tests]
```

---

## **🔄 WORKFLOW PROGRESSION**
After completing this task:
1. ✅ **Save individual function documentation** in `Task/tested_critical_function/`
2. ✅ **Create comprehensive summary** in `TEST_COVERAGE_SUMMARY.md`
3. 🔗 **This analysis will be used** for vulnerability assessment and test gap identification

---

## **💡 SUCCESS INDICATORS**
- [ ] All tested functions from `src/` folder documented
- [ ] Complete test method analysis for each function
- [ ] Security implications clearly identified
- [ ] Individual files created for each function
- [ ] Comprehensive summary report generated
- [ ] Test-to-source mapping completed accurately
- [ ] Documentation written in first-person ("I found...")

**Remember**: This test coverage analysis is crucial for identifying potential security gaps and understanding what logic has been validated. Focus on comprehensive mapping and security implications, not vulnerability analysis yet. 