1. FIRST
(A)
use this `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/coinbase/Task/AI_AGENT_WORKFLOW/Rules_guidelines/01_guides_for_validation/missingtested_critical_functions.md`, to find untetsted area or missed area  of math, logic, economic, precision or anything that could pose risk to this function `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/coinbase/Task/AI_AGENT_WORKFLOW/02_tested_critical_function/BalanceTracker_processFees.md`, dont assume, verify everything please

(B)
create the uncovered test in here `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/coinbase/assets_repos/contracts/test/vulnerabilities`, that follow your suggestion, make sure try that to test them like trying to break those uncovered areas


2. SECOND
use this `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/coinbase/Task/AI_AGENT_WORKFLOW/Rules_guidelines/tests_and_misstested_guide/02_finding_vuln_on_tested.md` to find some vulnerabilities here `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/coinbase/Task/AI_AGENT_WORKFLOW/02_tested_critical_function/BalanceTracker_constructor.md`, dont asume, verify

3. THIRD
START THIS TASK `Z_Task/AI_AGENT_WORKFLOW/Rules_guidelines/01_guides_for_validation/01_Instruction.md`, DONT SKIP ANYTHING


start tis task use this `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/coinbase/Task/AI_AGENT_WORKFLOW/Rules_guidelines/02_guides_for_validation/validating_vuln.md` to validate the vulnerability found here `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/coinbase/Task/REPORT/BalanceTracker_PublicInitialize_Hijack_validated.md` end-to-end , you can read `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/coinbase/Task/BOUNTY_OFFICIAL_DOCS/bounty_docs.md`, `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/coinbase/Task/BOUNTY_OFFICIAL_DOCS/assets_in_scope.md`, dont trust anything, dont assume, verify. the vulnerability also look too good to be true valid finding but verify it yourself with clear ouput of why, how and where this vulnerability is confirmed to be lavid or false-positive, dont be biased, be honest, look all angles, including deployment script and everything


STATIC ANALYSIS VALIDATION:

verify the vulnerabilities found here `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Asymmetry/Z_Task/static_analysis/slither_high_medium.md`, list the valid ones and the invalid ones, dont assume, verify each and every one of them , any one you found to be valid, report it here `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Asymmetry/Z_Task/static_analysis/valid_static`, using this template `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Asymmetry/Z_Task/manual_analysis/01_Stability_Pool_Pending_Yield_Capture.md` indivisually