STEP 1
read the related deployment script  and this here /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/Task/AI_AGENT_WORKFLOW/Rules_guidelines/guides_for_validation/target_areas_to_vuln.md to undertsand what's real vulnerability from false-positive, read the documentation here /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/Task/BOUNTY_OFFICIAL_DOCS/bounty_docs.md and code comment (if there is any) to undertsand the intended functionality,read the bounty information here /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/Task/BOUNTY_OFFICIAL_DOCS/bounty_information.md to confirm wether the vulnerability is in scope or not, verify wether the vulnerability is privileged or not,  read and understand the related test file to get a hint of the security practiced and intended security checks, then compare it with the test demonstrated here /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/2025-06-symbiotic-relay/middleware-sdk/test/vulnerabilities/Vault_SetDelegator_FrontRun.t.sol, and follow the log traces provided wether the vulnerability is really interacting with the vulnerable code or not, then read the report here /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/Task/REPORT/Vault_SetDelegator_FrontRun_Report.md to verify and confirm wether everything is in sync with the demonstrated test or not, read past report here /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/Task/AI_AGENT_WORKFLOW/Rules_guidelines/symbiotic_vul.md to understand how valid vulnerabilites are, make sure there is no exageration, no assumption, everything checks out, verify and confirm the economic, financial, mathematical , logical and practical soundness of the report and the test demonstration.

give me a step by step walkthrough of each execution in this function

start the task /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/Task/AI_AGENT_WORKFLOW/Rules_guidelines/guides_for_validation/rapid_validation_prompt.md, please verify everything, dont just assume what you read, verify it not matter how little it maybe

 STEP 2, A
find misalignment or sign of inconsistency in math, logic, economic and practical demonstration between the /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/Task/REPORT/verifyQuorumSig_MessageTruncation_Report.md and the /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/Task/BOUNTY_OFFICIAL_DOCS/bounty_docs.md, and also the test demonstration /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/2025-06-symbiotic-relay/middleware-sdk/test/vulnerabilities/test_VerifyQuorumSig_MessageTruncation.t.sol, make the answer short, very clear and basic to understand with why, how and where  all the necessary thing you need to say, dont make mistake and dont assume, follow the traces too, follow each execution, make sure the mitigation provided reallly fix the issue without inroducing any risk or another vulnerability and did not change the intended functionality of the code or the protocol in anyway. please verify end-to-end


B
make sure the fix is the best fix that doesnt introduce another vulnerability or risk, small or big, and make sure the fix is not changing the intended functionality or features of the protocol or the code that was fixed, dont make mistake please ,
now patched, compare the execution steps from the traces and compare the execution step from the function, to verify wether the test demonstration executes end-to-end without manipulation that could yield a false-positive result or not, please dont just read and understand, verify

STEP 3
A MUST DO : 
am sure you know how l2 chain, somehow a little semi centralized, using multisig sequence, base on symbiotic doc describe its middleware and the economic security its offering to projets wanting to launch their own decentralized layer1 or layer2, do you think this vulnerability is valid, if valid, state why, how and what makes you think it should be valid or vice versa, dont assume, dont  lie, verify before you make your verdict with proof.

STEP 4
function is public, it looks too good, to be true because , they cant leave it open like that, except if it is proected somewhere in the function flow or used somewhere instead of been called directly like that, maybe think, search and verify, we maybe missing something, use another alterntative critic you may find in the doc or codebase that points to making this vulnerability invalid and false-positive

STEP 5
i want you to reconfirm, check this docs /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/Task/BOUNTY_OFFICIAL_DOCS/bounty_docs.md and the deployment script that could render this vulnerability ineffective, like false-positive or something, why do you think this is valid or invalid, analyze it deeper, verify the accuracy of the vulnerability , here is the report /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/Task/REPORT/OperatorCap_DoS_Report.md and here is the test file /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/2025-06-symbiotic-relay/middleware-sdk/test/vulnerabilities/OperatorCap_DoS.t.sol to verify wether there is real interaction between the test and the implemented function where the vulnerability is, not just some mocks and console loggings and mathing

STEP 6
what are we looking at from this output, especially if you follow the traces carefully, does that proves the vulnerability or not, why and how you think this vulnerability is valid, did you consider other parts like modifiers, external contracts or oracles, or something that could render this vulnerablity ineffective, did you think of the economic feasibility, logical and mathematical soundness of the demonstration, what is the impact, likelihood and severity of the vulnerability, here is the test file /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/2025-06-symbiotic-relay/middleware-sdk/test/vulnerabilities/VaultSlasher_DoS_Exploit.t.sol, check wether the test is really demonstrating the vulnerability with real interaction with the vulnerable part or just mathing or using mock that may result in falsfe positive outcome. what could make the vulnerability invalid too, make the answer concise and short with enough proofs, learn more from the report too /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/Task/REPORT/Vault_Slasher_DoS_Report.md wether there is something off from the report that dispute the test or the impact, severity and likelihood of the vulnerability, dont lie, dont assume, verify and confirm.

STEP 7
Read the depoyment script and read the project readme here /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/2025-06-symbiotic-relay/README.md then read /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/Task/bounty_docs.md to understand the project perfectly, take your time to read it and reconfirm wether it is really vulnerable as reported here /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/Task/REPORT/OUT-OF-SCOPE/VaultFactory_Blacklist_Bypass_Report.md or not, also read the log and traces the execution to see wether the interaction is really doing the right one or just faking, dont lie, dont assume, make sure you really verify everything to avoid false-positive vulnerability



make the report in here /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/Task/REPORT/Vault_Donation_ShareManipulation_Report.md to look exactly like this template /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/Task/REPORT/template_sherlock.md, including the permalink and everything, dont make mistake