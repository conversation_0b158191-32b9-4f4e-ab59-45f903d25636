Use this `/Z_Task/AI_AGENT_WORKFLOW/Rules_guidelines/01_guides_for_validation/01_CRITICAL_FUNCTION_ANALYSIS.md ` to find critical functions in the ` /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Asymmetry/USDaf-v2/contracts/src ` directory (dont include dependencies), make sure the file is in scope according to ` /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Asymmetry/Z_Task/BOUNTY_OFFICIAL_DOCS/bounty_information.md `, focus on key areas, use this tempate for reporting any critical function found `Z_Task/AI_AGENT_WORKFLOW/01_list_of_critical_functions/01_smartsescrow_withdrawUnvestedTokens.md `


validate these functions ` /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/coinbase/Task/01_list_of_critical_functions`, going one after the other to remove duplicates or functions that are not not in scope according to the asset in scope here ` /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/coinbase/Task/ BOUNTY_OFFICIAL_DOCS/assets_in_scope.md ` or the bug bounty rules here ` /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/coinbase/Task/BOUNTY_OFFICIAL_DOCS/bounty_information.md `, dont lie, make no mistake, dont assume, verify before taking any action
