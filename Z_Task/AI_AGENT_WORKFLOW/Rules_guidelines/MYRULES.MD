# 🎯 Smart Contract Security Research Rules

## 🧠 CORE MINDSET
- Think like an attacker, not a developer
- Every function is guilty until proven innocent
- Question every assumption the code makes
- If something seems secure, try harder to break it
- Always validate findings with working exploits

## 📋 ANALYSIS WORKFLOW

### 1. **UNDERSTAND FIRST** (Before diving into code)
- What does this protocol do?
- Who are the actors? (users, admins, external contracts)
- Where is the money? (vaults, treasuries, critical state)
- How does value flow? (deposits, withdrawals, transfers)

### 2. **FOLLOW THE MONEY** (Priority targets)
- Focus on functions that handle funds
- Check access controls on critical functions
- Look for external calls that could be malicious
- Analyze price oracles and calculation logic

### 3. **QUESTION EVERYTHING**
- What if inputs are malicious?
- What if external calls fail or are malicious?
- What if functions are called in unexpected order?
- What if we use flash loans?

### 4. **PROVE IT WORKS** (No assumptions)
- Every vulnerability needs a test
- Show the exact attack path
- Calculate the economic impact
- Test on mainnet fork when possible

## 🛠 TECHNICAL RULES

### **File Organization**
```
PRE-REPORT/
├── list_of_critical_functions/
├── manual_analysis/
└── vulnerabilities/

test/
└── vulnerabilities/
```

### **When Analyzing Functions**
1. Start with the most complex part, understand related functions, modifiers etc, that protects the function
2. Follow this pattern:
   - Understand intended behavior by reading the provided doc or readme
   - Find ways to break assumptions
   - Chain vulnerabilities for bigger impact
   - Write a test to prove it

### **Common Attack Vectors** (Always check these)
- **Access Control**: Can unauthorized users call this?
- **Reentrancy**: External calls before state changes?
- **Math Issues**: Overflow, underflow, precision loss?
- **Oracle Manipulation**: Can prices be manipulated?
- **Flash Loans**: Can we exploit with borrowed funds?
- **DoS**: Can we make functions unusable?

### **Advanced Attack Vectors** (Complex & Often Missed)

#### **1. Comment-Code Discrepancy Attacks**
- **What**: Comments describe intended behavior, code does something else
- **How to Find**: 
  - Read EVERY comment carefully
  - Compare comment promises vs actual implementation
  - Look for "TODO", "FIXME", "HACK" comments
- **Example**: Comment says "only owner can withdraw" but modifier is missing

#### **2. Financial Market Assumption Violations**
- **Interest Rate Attacks**: Code assumes rates are always positive
- **Liquidity Assumptions**: Code assumes infinite liquidity exists
- **Price Continuity**: Code assumes prices change smoothly (no gaps)
- **Time Value Attacks**: Exploiting compound interest calculations
- **Example**: Using spot price instead of TWAP in lending protocols

#### **3. Solidity Language Quirks**
- **Storage Pointer Confusion**: Uninitialized storage pointers
- **Delegatecall Context Preservation**: `msg.sender` changes in complex calls
- **ABI Encoding Vulnerabilities**: Dynamic type encoding edge cases
- **Implicit Type Conversions**: uint256 to uint128 silent overflow
- **Example**: `bytes memory data` vs `bytes storage data` confusion

#### **4. Cross-Protocol Composability Attacks**
- **Callback Griefing**: Making other protocols' callbacks fail
- **State Dependency Attacks**: Protocol A depends on Protocol B's state
- **Liquidity Vampire Attacks**: Draining liquidity from integrated protocols
- **Example**: Manipulating Curve pool to affect protocols using it as oracle

#### **5. MEV & Block Production Attacks**
- **Sandwich Attacks**: Front-run + back-run user transactions
- **Block Stuffing**: Fill blocks to prevent liquidations
- **Time Bandit Attacks**: Reorg chains for profit
- **Uncle Bandit**: Steal transactions from uncle blocks
- **Example**: Front-running large trades on DEXs

#### **6. Precision Loss Accumulation**
- **Rounding Direction Manipulation**: Always round in attacker's favor
- **Dust Accumulation**: Small amounts that add up over time
- **Division Before Multiplication**: Catastrophic precision loss
- **Example**: Share calculation that loses 1 wei per transaction

#### **7. State Transition Race Conditions**
- **Multi-Block Attacks**: Attacks spanning multiple blocks
- **Partial State Updates**: State A updated but State B isn't
- **Cross-Contract Race**: Contract A and B update in wrong order
- **Example**: Update price oracle after using old price

#### **8. Economic Arbitrage Exploits**
- **Circular Trading**: A->B->C->A for profit
- **Cross-Chain Arbitrage**: Exploit price differences across chains
- **Fee Extraction**: Exploit fee mechanisms for profit
- **Example**: Borrow at 5%, lend at 10% using same collateral


### **Testing Requirements**
- Use Foundry for all tests
- Fork mainnet for realistic testing
- Every finding needs a PoC (Proof of Concept)
- Measure economic impact in USD

## ⚡ QUICK CHECKS

### **Red Flags** (Investigate immediately)
- `transfer()`, `transferFrom()`, `send()` without checks
- External calls without reentrancy protection
- Admin functions without proper access control
- Price calculations using spot prices
- Loops that users can manipulate

### **Focus Areas**
- **HIGH PRIORITY**: Fund handling, access control, external calls
- **MEDIUM PRIORITY**: Math operations, oracle usage, governance
- **LOW PRIORITY**: Gas optimization, code quality

## 📊 QUALITY STANDARDS

### **Before Reporting a Vulnerability**
✅ Can a regular user or even a sophisticated hacker exploit this?
✅ Is the economic impact > $1000?
✅ Does the exploit work on mainnet?
✅ Is the attack path realistic?

### **Report Must Include**
1. Clear vulnerability description
2. Step-by-step attack scenario
3. Working test code
4. Economic impact calculation
5. Recommended fix

## 🚀 WORKFLOW SHORTCUTS

### **Quick Start** (New Protocol)
```bash
# 1. Setup
mkdir -p PRE-REPORT/{list_of_critical_functions,manual_analysis,vulnerabilities}
mkdir -p test/vulnerabilities

# 2. Find critical contracts
find src/ -name "*.sol" | xargs grep -l "transfer\|withdraw\|deposit"

# 3. Run static analysis
slither . --print human-summary

# 4. Start with the money handlers
```

### **When Stuck**
- Go back to business logic understanding
- Think about combining multiple small issues
- Consider timing/ordering attacks
- Look at similar protocol exploits
- Use attack vector creatively

## 🎯 STRATEGIC VULNERABILITY HUNTING

### **Comment Analysis Strategy**
1. **Find Discrepancies**: 
   ```solidity
   // @notice Only admin can call this function
   function withdraw() external { // <-- Missing onlyAdmin modifier!
   ```
2. **Look for Warnings**:
   - "WARNING:", "CAREFUL:", "NOTE:"
   - "assume", "should", "must"
   - Version-specific comments (v1, v2, deprecated)

### **Financial Assumption Hunting**
1. **Search for Dangerous Assumptions**:
   ```bash
   grep -r "price.*constant\|always.*positive\|never.*zero" src/
   grep -r "TODO\|FIXME\|HACK\|XXX" src/
   ```
2. **Common Wrong Assumptions**:
   - "Price can never be zero" → Negative interest rates exist
   - "User will always have balance" → Flash loans = 0 balance
   - "This calculation never overflows" → What about in 10 years?

### **Solidity Edge Cases to Exploit**
1. **Memory vs Storage**:
   ```solidity
   struct Data { uint256[] values; }
   Data data; // storage pointer, uninitialized!
   ```
2. **Silent Failures**:
   - Low-level calls: `call()`, `delegatecall()`
   - Type conversions: `uint256` → `uint128`
   - External calls to non-existent contracts

### **Multi-Block Attack Patterns**
1. **Setup Phase** (Block N):
   - Manipulate oracle prices
   - Open positions
2. **Exploit Phase** (Block N+1):
   - Execute main attack
   - Use manipulated values
3. **Cleanup Phase** (Block N+2):
   - Close positions
   - Restore state

## 🎯 REMEMBER
- **Quality > Quantity**: One critical vulnerability > ten low issues
- **Think Creative**: The best bugs are the ones developers didn't imagine
- **Validate Everything**: No assumptions, only proofs
- **Document Well**: Future you will thank current you

## 📝 QUICK REFERENCE
- Full methodology: `INTEGRATED_SECURITY_RESEARCH_SYSTEM.md`
- Step-by-step: `EXECUTION_GUIDE.md`
- Hacker mindset: `08_My_BRAIN.md`
- Testing guide: `06_DYNAMIC_TESTING_AND_VALIDATION.md` 
- Vulnerabilities: `vulnerabilities.json`