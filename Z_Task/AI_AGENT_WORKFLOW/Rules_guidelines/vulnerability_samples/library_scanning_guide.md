# Library Vulnerability Quick-Scan Guide

Use this micro-workflow to sweep Solidity helper libraries for high-risk patterns and match them to known categories.

---

## 1. Collect Library Paths
```bash
# from repo root
fd -t d libraries src lib > .libpaths
```
This gathers typical library folders (e.g. `src/**/libraries/`, `lib/`).

---

## 2. Add a Grep Wrapper
```bash
# grepSol: regex search limited to Solidity libs
grepSol () {
  rg -n --color never -t Solidity "$1" $(cat .libpaths)
}
```

---

## 3. Common Pattern Snippets
| Pattern                | Example `grepSol` Regex |
|------------------------|--------------------------|
| External `call` w/o check | `\.call\(` |
| Delegatecall           | `delegatecall\(` |
| Self-destruct          | `selfdestruct\(` |
| Raw `ecrecover`        | `ecrecover\(` |
| Unbounded loop push    | `\.push\(` |
| Missing re-entrancy guard | `function .*withdraw.*public(?!.*noReentrant)` |

> For each hit, open the file and review logic / modifiers.

---

## 4. Cross-Reference Severity
After spotting a suspicious snippet, cross-check with the master pattern list:
```
Task/vuln_categories.md
```
Each entry there gives the category, impact, and mitigation tips (state validation, signature misuse, precision errors, liquidation DoS, etc.).  Match your finding and assign severity quickly.

---

## 5. Example Flow
```bash
# Find unsafe external calls
grepSol '\\.call(' | head -n 5
nvim +42 src/libraries/utils/UnsafeLib.sol
# Map to pattern → "Unchecked external call" (state validation)
```

---

### Optional: Daily Auto-Scan
```bash
(crontab -l; echo '0 3 * * * cd /path/to/repo && grepSol "\\.call(" > nightly_scan.txt') | crontab -
```
Generates a nightly report for new dangerous patterns.

---

> Keep this guide alongside `rapid_validation_prompt.md` for quick reference during audits. 