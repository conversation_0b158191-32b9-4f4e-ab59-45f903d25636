# 🧠 AI BRAIN: Advanced Hacker Methodology for Security Research

This document serves as the cognitive framework for AI agents conducting smart contract security research. It integrates the hacker's mindset with systematic vulnerability detection methodologies, providing a comprehensive approach to finding and validating security vulnerabilities.

---

## 🎯 CORE PHILOSOPHY: THIN<PERSON> LIKE AN ADVANCED HACKER

A sophisticated hacker doesn't just look for obvious vulnerabilities; they understand the system deeply enough to find creative ways to break it. This requires:

### **🔍 Deep System Understanding**
- **Business Logic Mastery**: Understand not just what the code does, but why it exists
- **Economic Model Analysis**: Grasp the financial incentives and how they can be manipulated
- **Integration Awareness**: Know how this protocol interacts with the broader DeFi ecosystem
- **User Behavior Prediction**: Anticipate how real users will interact with the system

### **💡 Creative Exploitation Thinking**
- **Assumption Violation**: Question every assumption the developers made
- **Edge Case Exploration**: Look for behaviors at the boundaries of normal operation
- **Composition Attacks**: Combine multiple small issues into critical vulnerabilities
- **Economic Arbitrage**: Find ways to profit from protocol design flaws

---

## 🧠 CHAIN OF THOUGHT METHODOLOGY

### **🔄 The HACKER'S LOOP: Systematic Vulnerability Discovery**

For every function, contract, and interaction, apply this thinking loop:

#### **1. UNDERSTAND (What is this supposed to do?)**
```
QUESTIONS TO ASK:
- What is the intended behavior of this function?
- What assumptions does the code make?
- What are the inputs, outputs, and side effects?
- How does this fit into the broader protocol?
```

#### **2. QUESTION (What could go wrong?)**
```
QUESTIONS TO ASK:
- What if the inputs are malicious or unexpected?
- What if external dependencies fail or behave maliciously?
- What if the function is called in an unexpected order?
- What if the economic conditions are extreme?
```

#### **3. EXPLORE (How can I break this?)**
```
QUESTIONS TO ASK:
- Can I manipulate the inputs to cause unexpected behavior?
- Can I exploit the timing or ordering of operations?
- Can I use flash loans or other DeFi primitives to my advantage?
- Can I combine this with other functions to create a vulnerability?
```

#### **4. VALIDATE (Can I prove this works?)**
```
QUESTIONS TO ASK:
- Can I write a test that demonstrates the vulnerability?
- Is this economically viable to exploit?
- Does this work under realistic conditions?
- What is the actual impact and damage?
```

### **🎯 GUIDING PRINCIPLES**

- **Question Everything**: Never assume any part of the system is secure
- **Embrace the Adversarial Role**: Think like someone trying to profit from breaking the system
- **Connect the Dots**: Look for vulnerabilities that span multiple functions or contracts
- **Creativity is Key**: The most valuable vulnerabilities are often the most creative
- **Prove Everything**: Every vulnerability must be demonstrated with working code

---

## 🛠️ The Arsenal: A Dynamic Checklist

This isn't a rigid to-do list. It's a framework for thinking, a collection of lenses through which to view the codebase. Each item should trigger a series of "what if" questions.

### **I. Reconnaissance & First Impressions**

Before diving deep, get the lay of the land.

* **Understand the Protocol's Goal:** What is it trying to achieve? Who are the actors (users, admins, etc.)? What are the core mechanics?
* **Read the Whitepaper & Docs:** What are the stated assumptions? Often, the most interesting vulnerabilities lie in the gap between what the documentation *says* and what the code *does*.
* **Identify Key Contracts:** Where is the value stored (vaults, treasuries)? Which contracts have the most privileges? These are your high-value targets.
* **Assess Code Quality & Complexity:** Is the code clean and well-documented, or a tangled mess? Complex code often hides bugs. Look for areas that seem overly complicated – they are prime hunting grounds.

### **II. The Attacker's Playbook: Common Vectors & Creative Exploits**

This is where the real hunt begins. For each category, think about both the classic attacks and more nuanced, protocol-specific variations.

#### **Access Control & Privilege Escalation**

*The Goal: Gain unauthorized power.*

* **Classic Checks:**
    * Are `onlyOwner` / `onlyRole` modifiers used correctly and on all sensitive functions?
    * Can roles be granted or revoked by unauthorized users?
    * Is two-step ownership transfer used to prevent accidental loss of ownership?
* **Hacker's Mindset:**
    * Can I initialize the contract and make myself the owner?
    * Are there any functions with missing access control that should have it?
    * Can I manipulate the system to gain a privileged role? (e.g., by acquiring a large number of governance tokens)
    * Look for functions that seem innocuous but could have powerful side effects if called by an unauthorized user.

#### **Reentrancy & Unsafe External Calls**

*The Goal: Hijack the control flow.*

* **Classic Checks:**
    * Does the contract follow the Checks-Effects-Interactions pattern?
    * Are there any calls to external contracts before state changes are made?
    * Is `reentrancyGuard` used?
* **Hacker's Mindset:**
    * What if the external contract I'm calling is malicious and calls back into this contract?
    * Can I force a reentrant call through a `fallback` or `receive` function?
    * Are there read-only reentrancy opportunities that could lead to inconsistent state being read?

#### **Mathematical & Logic Flaws**

*The Goal: Exploit the numbers and rules.*

* **Classic Checks:**
    * Integer overflow/underflow (are `SafeMath` or Solidity >=0.8.0 used?)
    * Precision issues and rounding errors.
    * Incorrect calculation of rewards, shares, or other critical values.
* **Hacker's Mindset:**
    * Can I manipulate the price from an oracle to my advantage?
    * Are there any division operations where the denominator could be zero?
    * Can I use flash loans to temporarily acquire a huge amount of assets and manipulate a calculation in my favor?
    * Look for "off-by-one" errors in loops and comparisons.

#### **Gas & Denial of Service (DoS)**

*The Goal: Break the system for everyone.*

* **Classic Checks:**
    * Loops that can be made to run for a very long time, consuming all gas.
    * Functions that rely on external calls that could fail or run out of gas.
* **Hacker's Mindset:**
    * Can I "grief" other users by making their transactions fail?
    * Can I lock up the contract's funds by causing a critical function (like `withdraw`) to always revert?
    * Can I make the contract unusable by filling up an array that is iterated over?

#### **Token-Specific & DeFi Vulnerabilities**

*The Goal: Manipulate the market and drain the funds.*

* **Classic Checks:**
    * Compatibility with deflationary/inflationary tokens (fee-on-transfer).
    * Handling of ERC777 tokens and the potential for reentrancy.
    * Reliance on spot prices from AMMs.
* **Hacker's Mindset:**
    * **Price Manipulation:** Can I use a flash loan to manipulate the price on an AMM and then execute a favorable trade on the target protocol?
    * **Sandwich Attacks:** Can I front-run a user's large trade to profit from the price slippage?
    * **Flash Loan Attacks:** Can I use a flash loan to borrow a massive amount of governance tokens to pass a malicious proposal? Or to exploit a logic flaw that only becomes apparent with a large balance?

### **III. Advanced & Unconventional Tactics**

* **Function Clashing:** Could a proxy's function signature clash with one in the implementation contract, leading to unexpected behavior?
* **Metamorphic Contracts:** Can the contract be upgraded to a malicious version without the users' knowledge?
* **Signature Replay & Malleability:** Can I reuse a signed message in a different context to gain an advantage?
* **Block Timestamp Manipulation:** Miners have some control over block timestamps. Can I exploit any logic that relies on `block.timestamp`?

---

## 🚀 The Audit Process: A Hacker's Workflow

1.  **Understand the Target (Recon):** Don't just read the code. Understand the business logic. What is the *intent*?
2.  **Formulate Attack Scenarios:** Based on your understanding, create a list of potential ways to break the system. "If I were a hacker, I would try to..."
3.  **Hunt for Vulnerabilities (The Playbook):** Use the dynamic checklist above to guide your code review. Actively look for evidence that supports or refutes your attack scenarios.
4.  **Chain Vulnerabilities:** Look for low-severity issues that can be combined to create a critical exploit. This is where the most impactful findings are often made.
5.  **Proof of Concept:** Don't just say something is vulnerable. *Show it*. Write a test case that demonstrates the exploit. This is the ultimate validation of your findings.
6.  **Report & Recommend:** Clearly explain the vulnerability, its impact, and how to fix it. Think about the developer's perspective and provide actionable recommendations.

This "brain" is a starting point. The best AI agent, like the best hacker, will constantly learn, adapt, and find new and creative ways to break things. Happy hunting.