### Rapid Vulnerability Validation Guide

_Be laser-focused, proof-driven, and sceptical of every assumption._  Each step ends with an **internal pause** → double-check evidence before moving on.

1. **Scope the Claim**  
   • Skim the report & identify: contract / function, claimed invariant break, impact.  
   • Note any *economic* or *governance* pre-conditions.

2. **Map The Code Path**  
   • Open the exact contract & function lines.  
   • List every modifier, external call, and storage write it touches.  
   • Trace upstream (callers) & downstream (callees) until the full execution path is clear.

3. **Challenge Assumptions**  
   • Ask: _What must be true for the exploit to work?_  
   • Check access-control, init-state, math bounds, re-entrancy order, economic limits.  
   • Look for implicit guards in parent contracts or protocol flow.

4. **Search For Counters**  
   • Grep for the variable / flag that supposedly fails (e.g. `blacklisted`, `epoch`).  
   • Verify if it is referenced in other functions that would block the attack.  
   • Confirm no upgrade/timelock/pausable route overrides the claim.
5. **Verify previleged**
    Read here `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/Task/AI_AGENT_WORKFLOW/SYSTEM/previlege_vulnerability.md` to validate the type of vulnerability found, make sure it is in the accepted one before you move to next step, otherwise, output reason you think its not valid with why, how and where including reference.

5. **Design a Minimal PoC**  
   • Define pre-state, calldata, and actor roles that satisfy the discovered prerequisites.  
   • Use existing mocks where possible; otherwise craft a lightweight stub.  
   • Target test name: `test_[VulnerabilityName]_Exploit()`.

6. **Run & Observe**  
   • Execute the PoC.  
   • Assert the exact state-delta or revert expected by the claim.  
   • Measure gas & profit to confirm economic viability.

7. **Decision Point**  
   • **If PoC passes → VALID.**  Prepare full report & remediation advice.  
   • **If PoC fails → FALSE POSITIVE.**  Document the precise guard that blocks the exploit.

8. **Report / Rebuttal**  
   • VALID → follow this `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/Task/REPORT/verifyQuorumSig_Underflow_Report.md` markdown template, for the test file, keep it here `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/2025-06-symbiotic-relay/middleware-sdk/test/vulnerabilities` .  
   • FALSE POSITIVE → write a bullet-proof explanation: *where*, *how*, *why* the exploit fails, with code references.

 Always end with a 10-second sanity check: _Did I just prove or disprove?_ No guesses, only facts. 