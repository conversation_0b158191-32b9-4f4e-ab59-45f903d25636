## Function: IMendiMarket.balanceOfUnderlying

### 📍 **Location & Path**
- **File**: `src/migration/IMigrator.sol`
- **Contract**: Interface IMendiMarket
- **Line Numbers**: Line 27
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/migration/IMigrator.sol/balanceOfUnderlying()`

### 📋 **Function Signature**
```solidity
function balanceOfUnderlying(address sender) external returns (uint256);
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: N/A (interface)
- **External Calls**: Used by `_collectMendiPositions` and `migrateAllPositions`
- **State Changes**: On external market: may accrue interest; non-view in interface
- **Events Emitted**: As per external implementation
- **Modifiers Applied**: N/A

### 📖 **Function Summary**
**What it does:** I found this call retrieves the user’s underlying balance held as cTokens; informs how much to migrate.
**Input Parameters:** `sender`
**Return Values:** Underlying amount
**Side Effects:** May trigger interest accrual depending on implementation

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Used for computing collateral to mint on v2. See `docs/src/src/migration/Migrator.sol/contract.Migrator.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: Medium
- **Financial Impact**: Indirect
- **External Dependencies**: Market’s accrual logic
- **Admin Privileges Required**: No

---
