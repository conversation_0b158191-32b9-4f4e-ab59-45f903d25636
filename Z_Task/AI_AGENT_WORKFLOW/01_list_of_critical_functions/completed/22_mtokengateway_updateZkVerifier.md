## Function: updateZkVerifier

### 📍 **Location & Path**
- **File**: `src/mToken/extension/mTokenGateway.sol`
- **Contract**: mTokenGateway
- **Line Numbers**: Lines 206 - 210
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/extension/mTokenGateway.sol/updateZkVerifier()`

### 📋 **Function Signature**
```solidity
function updateZkVerifier(address _zkVerifier) external onlyOwner {
    require(_zkVerifier != address(0), mTokenGateway_AddressNotValid());
    emit ZkVerifierUpdated(address(verifier), _zkVerifier);
    verifier = IZkVerifier(_zkVerifier);
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: None
- **External Calls**: None
- **State Changes**: Updates `verifier`
- **Events Emitted**: `ZkVerifierUpdated`
- **Modifiers Applied**: `onlyOwner`

### 📖 **Function Summary**
**What it does:** I found this rotates the zk verifier used in host/gateway proof checks.
**Input Parameters:** `_zkVerifier`
**Return Values:** None
**Side Effects:** Changes proof verification target

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Owner-controlled verifier update. See `docs/src/src/mToken/extension/mTokenGateway.sol/contract.mTokenGateway.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High
- **Financial Impact**: Yes
- **External Dependencies**: IZkVerifier implementation
- **Admin Privileges Required**: Yes

---
