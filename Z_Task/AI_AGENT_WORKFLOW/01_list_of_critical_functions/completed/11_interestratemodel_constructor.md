## Function: constructor

### 📍 **Location & Path**
- **File**: `src/interest/JumpRateModelV4.sol`
- **Contract**: JumpRateModelV4
- **Line Numbers**: Lines 79 - 91
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/interest/JumpRateModelV4.sol/constructor()`

### 📋 **Function Signature**
```solidity
constructor(
    uint256 blocksPerYear_,
    uint256 baseRatePerYear,
    uint256 multiplierPerYear,
    uint256 jumpMultiplierPerYear,
    uint256 kink_,
    address owner_,
    string memory name_
) Ownable(owner_) {
    blocksPerYear = blocksPerYear_;
    name = name_;
    _updateJumpRateModel(baseRatePerYear, multiplierPerYear, jumpMultiplierPerYear, kink_);
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: `_updateJumpRateModel(...)`
- **External Calls**: `Ownable(owner_)` constructor
- **State Changes**: Sets `blocksPerYear`, `name`, and initializes rate parameters via `_updateJumpRateModel`
- **Events Emitted**: `NewInterestParams(...)` (from `_updateJumpRateModel`)
- **Modifiers Applied**: None

### 📖 **Function Summary**
**What it does:** I found this constructor initializes the model’s core parameters and sets the owner. Misconfigured inputs here directly define the interest curve from deployment.
**Input Parameters:** Annualized rates, kink, owner address, name, and `blocksPerYear_`
**Return Values:** None
**Side Effects:** Emits initial `NewInterestParams` and sets ownership

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Constructor sets base parameters and ownership; rate params are derived via the internal updater. See `docs/src/src/interest/JumpRateModelV4.sol/contract.JumpRateModelV4.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High (initial parameters and owner define protocol economics and admin control)
- **Financial Impact**: Yes
- **External Dependencies**: None
- **Admin Privileges Required**: N/A (constructor, but sets admin and parameters)

---
