## Function: supplyOnHost

### 📍 **Location & Path**
- **File**: `src/mToken/extension/mTokenGateway.sol`
- **Contract**: mTokenGateway
- **Line Numbers**: Lines 224 - 252
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/extension/mTokenGateway.sol/supplyOnHost()`

### 📋 **Function Signature**
```solidity
function supplyOnHost(uint256 amount, address receiver, bytes4 lineaSelector)
    external
    payable
    override
    notPaused(OperationType.AmountIn)
    onlyAllowedUser(msg.sender)
    ifNotBlacklisted(msg.sender)
    ifNotBlacklisted(receiver)
{
    // checks
    require(amount > 0, mTokenGateway_AmountNotValid());
    require(msg.value >= gasFee, mTokenGateway_NotEnoughGasFee());

    IERC20(underlying).safeTransferFrom(msg.sender, address(this), amount);

    // effects
    accAmountIn[receiver] += amount;

    emit mTokenGateway_Supplied(
        msg.sender,
        receiver,
        accAmountIn[receiver],
        accAmountOut[receiver],
        amount,
        uint32(block.chainid),
        LINEA_CHAIN_ID,
        lineaSelector
    );
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: None
- **External Calls**: ERC20 `safeTransferFrom`
- **State Changes**: Increases `accAmountIn[receiver]`; receives ETH for gas fee
- **Events Emitted**: `mTokenGateway_Supplied`
- **Modifiers Applied**: `notPaused(OperationType.AmountIn)`, `onlyAllowedUser`, `ifNotBlacklisted`

### 📖 **Function Summary**
**What it does:** I found this ingests underlying tokens plus a gas fee, updating accounting for future extraction on host/destination.
**Input Parameters:** `amount`, `receiver`, `lineaSelector`
**Return Values:** None
**Side Effects:** Moves tokens in and ETH to contract; updates accounting

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Entry point for supplying tokens on host chain with zk-proof flows. See `docs/src/src/mToken/extension/mTokenGateway.sol/contract.mTokenGateway.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High (funds inflow and accounting)
- **Financial Impact**: Yes
- **External Dependencies**: ERC20 token compliance
- **Admin Privileges Required**: No

---
