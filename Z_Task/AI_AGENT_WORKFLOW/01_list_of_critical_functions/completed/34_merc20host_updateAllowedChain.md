## Function: updateAllowed<PERSON>hain

### 📍 **Location & Path**
- **File**: `src/mToken/host/mErc20Host.sol`
- **Contract**: mErc20Host
- **Line Numbers**: Lines 129 - 134
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/host/mErc20Host.sol/updateAllowedChain()`

### 📋 **Function Signature**
```solidity
function updateAllowedChain(uint32 _chainId, bool _status) external {
    _onlyAdminOrRole(_getChainsManagerRole());

    allowedChains[_chainId] = _status;
    emit mErc20Host_ChainStatusUpdated(_chainId, _status);
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: `_onlyAdminOrRole`, `_getChainsManagerRole`
- **External Calls**: None
- **State Changes**: Updates `allowedChains`
- **Events Emitted**: `mErc20Host_ChainStatusUpdated`
- **Modifiers Applied**: Inline admin/role check

### 📖 **Function Summary**
Controls which chains are permitted in cross-chain operations; wrong settings can block or unlock flows.

### 📚 **Expected Behavior (Per Docs)**
See `docs/src/src/mToken/host/mErc20Host.sol/contract.mErc20Host.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High
- **Financial Impact**: Yes
- **External Dependencies**: Role config
- **Admin Privileges Required**: Yes

---
