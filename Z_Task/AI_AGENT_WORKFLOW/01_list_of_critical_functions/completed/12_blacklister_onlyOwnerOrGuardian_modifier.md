## Modifier: onlyOwnerOrGuardian

### 📍 **Location & Path**
- **File**: `src/blacklister/Blacklister.sol`
- **Contract**: Blacklister
- **Line Numbers**: Lines 53 - 56
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/blacklister/Blacklister.sol/onlyOwnerOrGuardian`

### 📋 **Modifier Code**
```solidity
modifier onlyOwnerOrGuardian() {
    require(msg.sender == owner() || rolesOperator.isAllowedFor(msg.sender, rolesOperator.GUARDIAN_BLACKLIST()), Blacklister_NotAllowed());
    _;
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: `owner()` from `OwnableUpgradeable`
- **External Calls**: `rolesOperator.isAllowedFor(msg.sender, rolesOperator.GUARDIAN_BLACKLIST())`
- **State Changes**: None
- **Events Emitted**: None
- **Applied To**: `blacklist(address)`, `unblacklist(address)`

### 📖 **Summary**
**What it does:** I found this modifier restricts sensitive blacklist operations to either the contract owner or a caller possessing the `GUARDIAN_BLACKLIST` role in `rolesOperator`.
**Inputs/Outputs:** N/A
**Side Effects:** Gatekeeping for critical state-changing functions

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Blacklist actions must be owner or designated guardian only. See `docs/src/src/blacklister/Blacklister.sol/contract.Blacklister.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High (improper role configuration can allow/deny blacklist power)
- **Financial Impact**: Indirect but potentially systemic
- **External Dependencies**: `IRoles` implementation and constants
- **Admin Privileges Required**: Enforced by this modifier

---
