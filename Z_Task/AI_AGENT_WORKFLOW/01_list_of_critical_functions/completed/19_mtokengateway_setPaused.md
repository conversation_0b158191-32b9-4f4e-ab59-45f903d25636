## Function: setPaused

### 📍 **Location & Path**
- **File**: `src/mToken/extension/mTokenGateway.sol`
- **Contract**: mTokenGateway
- **Line Numbers**: Lines 159 - 171
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/extension/mTokenGateway.sol/setPaused()`

### 📋 **Function Signature**
```solidity
function setPaused(OperationType _type, bool state) external override {
    if (state) {
        require(
            msg.sender == owner() || rolesOperator.isAllowedFor(msg.sender, rolesOperator.GUARDIAN_PAUSE()),
            mTokenGateway_CallerNotAllowed()
        );
    } else {
        require(msg.sender == owner(), mTokenGateway_CallerNotAllowed());
    }

    emit mTokenGateway_PausedState(_type, state);
    paused[_type] = state;
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: None
- **External Calls**: `rolesOperator.isAllowedFor(...)`
- **State Changes**: Updates `paused` mapping
- **Events Emitted**: `mTokenGateway_PausedState`
- **Modifiers Applied**: None

### 📖 **Function Summary**
**What it does:** I found this function toggles pause states for operation types with stricter unpause control (owner only) and pause by guardian allowed.
**Input Parameters:** `_type`, `state`
**Return Values:** None
**Side Effects:** Enables/Disables critical operations

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Pause control enabling guardian to pause, owner to unpause. See `docs/src/src/mToken/extension/mTokenGateway.sol/contract.mTokenGateway.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High (global operation switches)
- **Financial Impact**: Yes (can freeze or enable flows)
- **External Dependencies**: IRoles
- **Admin Privileges Required**: Yes

---
