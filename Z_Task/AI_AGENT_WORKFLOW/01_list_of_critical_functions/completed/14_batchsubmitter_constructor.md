## Function: constructor

### 📍 **Location & Path**
- **File**: `src/mToken/BatchSubmitter.sol`
- **Contract**: BatchSubmitter
- **Line Numbers**: Lines 80 - 85
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/BatchSubmitter.sol/constructor()`

### 📋 **Function Signature**
```solidity
constructor(address _roles, address _zkVerifier, address _owner) Ownable(_owner) {
    require(_roles != address(0), BatchSubmitter_AddressNotValid());
    require(_zkVerifier != address(0), BatchSubmitter_AddressNotValid());
    rolesOperator = IRoles(_roles);
    verifier = IZkVerifier(_zkVerifier);
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: None
- **External Calls**: `Ownable(_owner)`
- **State Changes**: Sets `rolesOperator` and `verifier`
- **Events Emitted**: None
- **Modifiers Applied**: None

### 📖 **Function Summary**
**What it does:** I found this sets core access control and proof verifier dependencies for all future batch operations.
**Input Parameters:** `_roles`, `_zkVerifier`, `_owner`
**Return Values:** None
**Side Effects:** Initializes critical dependencies

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Constructor wires the roles and ZK verifier. See `docs/src/src/mToken/BatchSubmitter.sol/contract.BatchSubmitter.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High (misconfigured verifier/roles can enable fraudulent batches)
- **Financial Impact**: Yes (downstream batched mints/repays/withdraws)
- **External Dependencies**: IRoles, IZkVerifier
- **Admin Privileges Required**: N/A (constructor)

---
