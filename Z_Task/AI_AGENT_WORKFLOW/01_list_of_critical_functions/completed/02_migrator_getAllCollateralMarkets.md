## Function: getAllCollateralMarkets

### 📍 **Location & Path**
- **File**: `src/migration/Migrator.sol`
- **Contract**: Migrator
- **Line Numbers**: Lines 58 - 71
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/migration/Migrator.sol/getAllCollateralMarkets()`

### 📋 **Function Signature**
```solidity
function getAllCollateralMarkets(address user) external view returns (address[] memory markets) {
    IMendiMarket[] memory mendiMarkets = IMendiComptroller(MENDI_COMPTROLLER).getAssetsIn(user);

    uint256 marketsLength = mendiMarkets.length;
    markets = new address[](marketsLength);
    for (uint256 i = 0; i < marketsLength; i++) {
        markets[i] = address(0);
        IMendiMarket mendiMarket = mendiMarkets[i];
        uint256 balanceOfCTokens = mendiMarket.balanceOf(user);
        if (balanceOfCTokens > 0) {
            markets[i] = address(mendiMarket);
        }
    }
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: None
- **External Calls**: `IMendiComptroller.getAssetsIn(user)`, `IMendiMarket.balanceOf(user)` per market
- **State Changes**: None (view)
- **Events Emitted**: None
- **Modifiers Applied**: None

### 📖 **Function Summary**
**What it does:** I found this function enumerates Mendi markets where `user` has cToken balances (collateral).
**Input Parameters:** `user`
**Return Values:** Array of market addresses (or zero-address if not collateralized)
**Side Effects:** None

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Helper to identify collateral markets on the legacy platform. See `docs/src/src/migration/Migrator.sol/contract.Migrator.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: Low/Medium (data used to inform migration decisions)
- **Financial Impact**: Indirect
- **External Dependencies**: Mendi Comptroller and Market interfaces
- **Admin Privileges Required**: No

---
