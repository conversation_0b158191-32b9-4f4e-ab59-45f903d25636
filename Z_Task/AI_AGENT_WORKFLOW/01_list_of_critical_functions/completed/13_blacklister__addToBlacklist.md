## Function: _addToBlacklist

### 📍 **Location & Path**
- **File**: `src/blacklister/Blacklister.sol`
- **Contract**: Blacklister
- **Line Numbers**: Lines 78 - 82
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/blacklister/Blacklister.sol/_addToBlacklist()`

### 📋 **Function Signature**
```solidity
function _addToBlacklist(address user) internal {
    isBlacklisted[user] = true;
    _blacklistedList.push(user);
    emit Blacklisted(user);
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: None
- **External Calls**: None
- **State Changes**: Sets mapping flag and appends to `_blacklistedList`
- **Events Emitted**: `Blacklisted(user)`
- **Modifiers Applied**: None (internal)

### 📖 **Function Summary**
**What it does:** I found this internal function sets the user’s blacklist status and tracks it in the array.
**Input Parameters:** `user`
**Return Values:** None
**Side Effects:** Emits event and grows the list

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Internal helper to perform the blacklist state change. See `docs/src/src/blacklister/Blacklister.sol/contract.Blacklister.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: Medium (invariant: array may include duplicates if called without pre-checks; mapping is source of truth)
- **Financial Impact**: Indirect
- **External Dependencies**: None
- **Admin Privileges Required**: Indirect (reachable via guarded external functions)

---
