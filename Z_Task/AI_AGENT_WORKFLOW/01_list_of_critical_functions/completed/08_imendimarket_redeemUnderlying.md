## Function: IMendiMarket.redeemUnderlying

### 📍 **Location & Path**
- **File**: `src/migration/IMigrator.sol`
- **Contract**: Interface IMendiMarket
- **Line Numbers**: Line 22
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/migration/IMigrator.sol/redeemUnderlying()`

### 📋 **Function Signature**
```solidity
function redeemUnderlying(uint256 redeemAmount) external returns (uint256);
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: N/A (interface)
- **External Calls**: Used by `Migrator.migrateAllPositions()` to withdraw underlying after debt repayment
- **State Changes**: On the external Mendi market: burns cTokens and releases underlying
- **Events Emitted**: As per external implementation
- **Modifiers Applied**: N/A

### 📖 **Function Summary**
**What it does:** I found this external call redeems underlying collateral from v1 to be transferred into v2.
**Input Parameters:** `redeemAmount`
**Return Values:** Error code (0 expected)
**Side Effects:** Transfers underlying to caller

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Part of withdrawal step; failure must revert per check. See `docs/src/src/migration/Migrator.sol/contract.Migrator.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High
- **Financial Impact**: Yes
- **External Dependencies**: Mendi market behavior
- **Admin Privileges Required**: No

---
