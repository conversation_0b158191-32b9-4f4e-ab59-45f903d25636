## Function: IMendiMarket.underlying

### 📍 **Location & Path**
- **File**: `src/migration/IMigrator.sol`
- **Contract**: Interface IMendiMarket
- **Line Numbers**: Line 24
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/migration/IMigrator.sol/underlying()`

### 📋 **Function Signature**
```solidity
function underlying() external view returns (address);
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: N/A (interface)
- **External Calls**: Used by `_collectMendiPositions` → `_getMaldaMarket` to map underlying to a Malda market
- **State Changes**: None (view)
- **Events Emitted**: N/A
- **Modifiers Applied**: N/A

### 📖 **Function Summary**
**What it does:** I found this call returns the ERC20 underlying of the v1 market, essential for correctly pairing to the corresponding v2 market.
**Input Parameters:** None
**Return Values:** Underlying token address
**Side Effects:** None

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Required to match markets by underlying during migration. See `docs/src/src/migration/Migrator.sol/contract.Migrator.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High (incorrect mapping could send assets to wrong market)
- **Financial Impact**: Yes
- **External Dependencies**: Mendi market correctness
- **Admin Privileges Required**: No

---
