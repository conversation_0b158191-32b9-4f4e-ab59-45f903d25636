## Function: updateZkVerifier

### 📍 **Location & Path**
- **File**: `src/mToken/BatchSubmitter.sol`
- **Contract**: BatchSubmitter
- **Line Numbers**: Lines 92 - 96
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/BatchSubmitter.sol/updateZkVerifier()`

### 📋 **Function Signature**
```solidity
function updateZkVerifier(address _zkVerifier) external onlyOwner {
    require(_zkVerifier != address(0), BatchSubmitter_AddressNotValid());
    emit ZkVerifierUpdated(address(verifier), _zkVerifier);
    verifier = IZkVerifier(_zkVerifier);
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: None
- **External Calls**: None
- **State Changes**: Updates `verifier`
- **Events Emitted**: `ZkVerifierUpdated(old, new)`
- **Modifiers Applied**: `onlyOwner`

### 📖 **Function Summary**
**What it does:** I found this updates the verifier contract used to validate journals/proofs for batch processing.
**Input Parameters:** `_zkVerifier`
**Return Values:** None
**Side Effects:** Changes verification backend

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Owner can rotate verifier. See `docs/src/src/mToken/BatchSubmitter.sol/contract.BatchSubmitter.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High (changing verifier affects proof validity)
- **Financial Impact**: Yes (invalid proofs may pass/fail)
- **External Dependencies**: IZkVerifier correctness
- **Admin Privileges Required**: Yes

---
