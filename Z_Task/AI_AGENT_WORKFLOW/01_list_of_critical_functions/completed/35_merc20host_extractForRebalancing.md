## Function: extractForRebalancing

### 📍 **Location & Path**
- **File**: `src/mToken/host/mErc20Host.sol`
- **Contract**: mErc20Host
- **Line Numbers**: Lines 139 - 144
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/host/mErc20Host.sol/extractForRebalancing()`

### 📋 **Function Signature**
```solidity
function extractForRebalancing(uint256 amount) external {
    IOperatorDefender(operator).beforeRebalancing(address(this));

    if (!_isAllowedFor(msg.sender, rolesOperator.REBALANCER())) revert mErc20Host_NotRebalancer();
    IERC20(underlying).safeTransfer(msg.sender, amount);
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: `_isAllowedFor`
- **External Calls**: `IOperatorDefender.beforeRebalancing`, `IERC20.safeTransfer`
- **State Changes**: Token transfer out
- **Events Emitted**: None
- **Modifiers Applied**: None

### 📖 **Function Summary**
Rebalancer-controlled asset extraction; wrong role gating enables unauthorized outflows.

### 📚 **Expected Behavior (Per Docs)**
See `docs/src/src/mToken/host/mErc20Host.sol/contract.mErc20Host.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High
- **Financial Impact**: Yes
- **External Dependencies**: Operator defender, ERC20
- **Admin Privileges Required**: Role-based (REBALANCER)

---
