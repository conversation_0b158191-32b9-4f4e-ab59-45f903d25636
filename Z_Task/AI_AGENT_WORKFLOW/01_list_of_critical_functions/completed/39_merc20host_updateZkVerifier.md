## Function: updateZkVerifier

### 📍 **Location & Path**
- **File**: `src/mToken/host/mErc20Host.sol`
- **Contract**: mErc20Host
- **Line Numbers**: Lines 179 - 183
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/host/mErc20Host.sol/updateZkVerifier()`

### 📋 **Function Signature**
```solidity
function updateZkVerifier(address _zkVerifier) external onlyAdmin {
    require(_zkVerifier != address(0), mErc20Host_AddressNotValid());
    emit ZkVerifierUpdated(address(verifier), _zkVerifier);
    verifier = IZkVerifier(_zkVerifier);
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: None
- **External Calls**: None
- **State Changes**: Updates `verifier`
- **Events Emitted**: `ZkVerifierUpdated`
- **Modifiers Applied**: `onlyAdmin`

### 📖 **Function Summary**
Verifier rotation; wrong verifier breaks proof gate or allows invalid proofs.

### 📚 **Expected Behavior (Per Docs)**
See `docs/src/src/mToken/host/mErc20Host.sol/contract.mErc20Host.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High
- **Financial Impact**: Yes
- **External Dependencies**: IZkVerifier
- **Admin Privileges Required**: Yes

---
