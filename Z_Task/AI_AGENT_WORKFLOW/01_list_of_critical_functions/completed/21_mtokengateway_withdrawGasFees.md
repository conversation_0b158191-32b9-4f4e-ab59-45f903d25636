## Function: withdrawGasFees

### 📍 **Location & Path**
- **File**: `src/mToken/extension/mTokenGateway.sol`
- **Contract**: mTokenGateway
- **Line Numbers**: Lines 194 - 200
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/extension/mTokenGateway.sol/withdrawGasFees()`

### 📋 **Function Signature**
```solidity
function withdrawGasFees(address payable receiver) external {
    if (msg.sender != owner() && !_isAllowedFor(msg.sender, _getSequencerRole())) {
        revert mTokenGateway_CallerNotAllowed();
    }
    uint256 balance = address(this).balance;
    receiver.transfer(balance);
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: `_isAllowedFor`, `_getSequencerRole`
- **External Calls**: Native transfer
- **State Changes**: Sends ETH balance to `receiver`
- **Events Emitted**: None
- **Modifiers Applied**: None

### 📖 **Function Summary**
**What it does:** I found this lets the owner or sequencer withdraw accumulated gas fees.
**Input Parameters:** `receiver`
**Return Values:** None
**Side Effects:** Moves ETH

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Controlled gas fee withdrawal. See `docs/src/src/mToken/extension/mTokenGateway.sol/contract.mTokenGateway.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: Medium/High (ETH outflow)
- **Financial Impact**: Yes
- **External Dependencies**: Sequencer role correctness
- **Admin Privileges Required**: Owner or Sequencer role

---
