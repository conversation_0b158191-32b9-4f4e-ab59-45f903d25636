## Function: liquidateExternal

### 📍 **Location & Path**
- **File**: `src/mToken/host/mErc20Host.sol`
- **Contract**: mErc20Host
- **Line Numbers**: Lines 197 - 222
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/host/mErc20Host.sol/liquidateExternal()`

### 📋 **Function Signature**
```solidity
function liquidateExternal(
    bytes calldata journalData,
    bytes calldata seal,
    address[] calldata userToLiquidate,
    uint256[] calldata liquidateAmount,
    address[] calldata collateral,
    address receiver
) external override {
    // verify received data
    if (!_isAllowedFor(msg.sender, _getBatchProofForwarderRole())) {
        _verifyProof(journalData, seal);
    }

    bytes[] memory journals = _decodeJournals(journalData);
    uint256 length = journals.length;
    CommonLib.checkLengthMatch(length, liquidateAmount.length);
    CommonLib.checkLengthMatch(length, userToLiquidate.length);
    CommonLib.checkLengthMatch(length, collateral.length);

    for (uint256 i; i < length;) {
        _liquidateExternal(journals[i], userToLiquidate[i], liquidateAmount[i], collateral[i], receiver);
        unchecked {
            ++i;
        }
    }
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: `_verifyProof`, `_decodeJournals`, `_liquidateExternal`
- **External Calls**: None here; liquidation happens in internal callee
- **State Changes**: None here
- **Events Emitted**: None here
- **Modifiers Applied**: None; role check inline

### 📖 **Function Summary**
Proof-gated cross-chain liquidation executor; incorrect verification or length checks can break liquidation invariants.

### 📚 **Expected Behavior (Per Docs)**
See `docs/src/src/mToken/host/mErc20Host.sol/contract.mErc20Host.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High
- **Financial Impact**: Yes
- **External Dependencies**: IZkVerifier, role config, decoder lib
- **Admin Privileges Required**: Role-based bypass

---
