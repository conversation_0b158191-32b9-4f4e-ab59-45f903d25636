## Function: withdrawGasFees

### 📍 **Location & Path**
- **File**: `src/mToken/host/mErc20Host.sol`
- **Contract**: mErc20Host
- **Line Numbers**: Lines 168 - 173
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/host/mErc20Host.sol/withdrawGasFees()`

### 📋 **Function Signature**
```solidity
function withdrawGasFees(address payable receiver) external {
    _onlyAdminOrRole(_getSequencerRole());

    uint256 balance = address(this).balance;
    receiver.transfer(balance);
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: `_onlyAdminOrRole`, `_getSequencerRole`
- **External Calls**: Native transfer
- **State Changes**: Sends ETH
- **Events Emitted**: None
- **Modifiers Applied**: Inline access control

### 📖 **Function Summary**
Sequencer/admin-controlled ETH withdrawal; mis-gated can drain fees.

### 📚 **Expected Behavior (Per Docs)**
See `docs/src/src/mToken/host/mErc20Host.sol/contract.mErc20Host.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: Medium/High
- **Financial Impact**: Yes
- **External Dependencies**: Role config
- **Admin Privileges Required**: Yes/role

---
