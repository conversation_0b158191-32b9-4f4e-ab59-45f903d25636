## Function: unblacklist

### 📍 **Location & Path**
- **File**: `src/blacklister/Blacklister.sol`
- **Contract**: Blacklister
- **Line Numbers**: Lines 69 - 74
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/blacklister/Blacklister.sol/unblacklist()`

### 📋 **Function Signature**
```solidity
function unblacklist(address user) external override onlyOwnerOrGuardian {
    if (!isBlacklisted[user]) revert Blacklister_NotBlacklisted();
    isBlacklisted[user] = false;
    _removeFromBlacklistList(user);
    emit Unblacklisted(user);
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: `_removeFromBlacklistList(address)`
- **External Calls**: Via modifier `onlyOwnerOrGuardian` → `rolesOperator.isAllowedFor(msg.sender, rolesOperator.GUARDIAN_BLACKLIST())`; `owner()` from `OwnableUpgradeable`
- **State Changes**: Sets `isBlacklisted[user] = false` and removes `user` from `_blacklistedList`
- **Events Emitted**: `Unblacklisted(user)`
- **Modifiers Applied**: `onlyOwnerOrGuardian`

### 📖 **Function Summary**
**What it does:** I found this function removes `user` from blacklist and updates internal list.
**Input Parameters:**
- `user`: Address to be unblacklisted
**Return Values:** None
**Side Effects:** Updates blacklist mapping and list; emits `Unblacklisted`

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** The docs enumerate `unblacklist(address)` as removing a user from blacklist, restricted to owner or guardian roles. See `docs/src/src/blacklister/Blacklister.sol/contract.Blacklister.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: Medium
- **Financial Impact**: Indirect
- **External Dependencies**: `IRoles.rolesOperator` and its `GUARDIAN_BLACKLIST` role
- **Admin Privileges Required**: Yes (owner or guardian)

---
