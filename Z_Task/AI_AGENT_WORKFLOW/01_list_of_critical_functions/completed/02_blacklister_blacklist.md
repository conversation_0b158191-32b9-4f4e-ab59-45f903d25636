## Function: blacklist

### 📍 **Location & Path**
- **File**: `src/blacklister/Blacklister.sol`
- **Contract**: Blacklister
- **Line Numbers**: Lines 64 - 67
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/blacklister/Blacklister.sol/blacklist()`

### 📋 **Function Signature**
```solidity
function blacklist(address user) external override onlyOwnerOrGuardian {
    if (isBlacklisted[user]) revert Blacklister_AlreadyBlacklisted();
    _addToBlacklist(user);
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: `_addToBlacklist(address)`
- **External Calls**: Via modifier `onlyOwnerOrGuardian` → `rolesOperator.isAllowedFor(msg.sender, rolesOperator.GUARDIAN_BLACKLIST())`; `owner()` from `OwnableUpgradeable`
- **State Changes**: Sets `isBlacklisted[user] = true` and appends `user` to `_blacklistedList` (inside `_addToBlacklist`)
- **Events Emitted**: `Blacklisted(user)` (inside `_addToBlacklist`)
- **Modifiers Applied**: `onlyOwnerOrGuardian`

### 📖 **Function Summary**
**What it does:** I found this function marks `user` as blacklisted and records the address in the blacklist array.
**Input Parameters:**
- `user`: Address to be blacklisted
**Return Values:** None
**Side Effects:** Updates blacklist mapping and list; emits `Blacklisted`

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** The docs enumerate `blacklist(address)` as adding a user to blacklist, restricted to owner or guardian roles. See `docs/src/src/blacklister/Blacklister.sol/contract.Blacklister.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: Medium
- **Financial Impact**: Indirect (controls ability of addresses to interact elsewhere)
- **External Dependencies**: `IRoles.rolesOperator` and its `GUARDIAN_BLACKLIST` role
- **Admin Privileges Required**: Yes (owner or guardian)

---
