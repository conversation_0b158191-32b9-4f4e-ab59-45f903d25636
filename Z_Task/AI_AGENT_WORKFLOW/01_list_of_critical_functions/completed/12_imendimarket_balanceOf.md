## Function: IMendiMarket.balanceOf

### 📍 **Location & Path**
- **File**: `src/migration/IMigrator.sol`
- **Contract**: Interface IMendiMarket
- **Line Numbers**: Line 26
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/migration/IMigrator.sol/balanceOf()`

### 📋 **Function Signature**
```solidity
function balanceOf(address sender) external view returns (uint256);
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: N/A (interface)
- **External Calls**: Used by `getAllCollateralMarkets` and `migrateAllPositions` to determine cToken holdings
- **State Changes**: None (view)
- **Events Emitted**: N/A
- **Modifiers Applied**: N/A

### 📖 **Function Summary**
**What it does:** I found this call reads the user’s cToken balance to identify collateral presence and size migration operations.
**Input Parameters:** `sender`
**Return Values:** cToken balance
**Side Effects:** None

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Used for collateral presence checks and transfer sizing. See `docs/src/src/migration/Migrator.sol/contract.Migrator.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: Medium
- **Financial Impact**: Indirect
- **External Dependencies**: Accurate token accounting
- **Admin Privileges Required**: No

---
