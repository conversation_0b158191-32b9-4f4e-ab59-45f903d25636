## Function: outHere

### 📍 **Location & Path**
- **File**: `src/mToken/extension/mTokenGateway.sol`
- **Contract**: mTokenGateway
- **Line Numbers**: Lines 257 - 279
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/extension/mTokenGateway.sol/outHere()`

### 📋 **Function Signature**
```solidity
function outHere(bytes calldata journalData, bytes calldata seal, uint256[] calldata amounts, address receiver)
    external
    notPaused(OperationType.AmountOutHere)
    ifNotBlacklisted(msg.sender)
    ifNotBlacklisted(receiver)
{
    // verify received data
    if (!rolesOperator.isAllowedFor(msg.sender, rolesOperator.PROOF_BATCH_FORWARDER())) {
        _verifyProof(journalData, seal);
    }

    bytes[] memory journals = abi.decode(journalData, (bytes[]));
    uint256 length = journals.length;
    require(length == amounts.length, mTokenGateway_LengthNotValid());

    for (uint256 i; i < journals.length;) {
        _outHere(journals[i], amounts[i], receiver);

        unchecked {
            ++i;
        }
    }
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: `_verifyProof`, `_outHere`
- **External Calls**: None directly here; ERC20 transfers happen in `_outHere`
- **State Changes**: None here
- **Events Emitted**: None here (emitted in `_outHere`)
- **Modifiers Applied**: `notPaused(OperationType.AmountOutHere)`, `ifNotBlacklisted`

### 📖 **Function Summary**
**What it does:** I found this validates proofs (unless privileged) and dispatches per-journal extractions via `_outHere`.
**Input Parameters:** `journalData`, `seal`, `amounts`, `receiver`
**Return Values:** None
**Side Effects:** Drives token outflows per journal

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Main extraction path on destination chain. See `docs/src/src/mToken/extension/mTokenGateway.sol/contract.mTokenGateway.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High (controls token outflows)
- **Financial Impact**: Yes
- **External Dependencies**: IZkVerifier, IRoles
- **Admin Privileges Required**: Role-based bypass for proof check

---
