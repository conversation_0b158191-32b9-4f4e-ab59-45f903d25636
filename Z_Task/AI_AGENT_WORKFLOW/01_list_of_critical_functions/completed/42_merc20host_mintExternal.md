## Function: mintExternal

### 📍 **Location & Path**
- **File**: `src/mToken/host/mErc20Host.sol`
- **Contract**: mErc20Host
- **Line Numbers**: Lines 227 - 250
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/host/mErc20Host.sol/mintExternal()`

### 📋 **Function Signature**
```solidity
function mintExternal(
    bytes calldata journalData,
    bytes calldata seal,
    uint256[] calldata mintAmount,
    uint256[] calldata minAmountsOut,
    address receiver
) external override {
    if (!_isAllowedFor(msg.sender, _getBatchProofForwarderRole())) {
        _verifyProof(journalData, seal);
    }

    _checkOutflow(CommonLib.computeSum(mintAmount));

    bytes[] memory journals = _decodeJournals(journalData);
    uint256 length = journals.length;
    CommonLib.checkLengthMatch(length, mintAmount.length);

    for (uint256 i; i < length;) {
        _mintExternal(journals[i], mintAmount[i], minAmountsOut[i], receiver);
        unchecked {
            ++i;
        }
    }
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: `_verifyProof`, `_checkOutflow`, `_decodeJournals`, `_mintExternal`
- **External Calls**: None here
- **State Changes**: None here
- **Events Emitted**: None here
- **Modifiers Applied**: None

### 📖 **Function Summary**
Cross-chain proof-gated mint execution; enforces outflow limits and minimum amount constraints in internal flow.

### 📚 **Expected Behavior (Per Docs)**
See `docs/src/src/mToken/host/mErc20Host.sol/contract.mErc20Host.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High
- **Financial Impact**: Yes
- **External Dependencies**: IZkVerifier, Operator defender, decoder lib
- **Admin Privileges Required**: Role-based bypass for proof

---
