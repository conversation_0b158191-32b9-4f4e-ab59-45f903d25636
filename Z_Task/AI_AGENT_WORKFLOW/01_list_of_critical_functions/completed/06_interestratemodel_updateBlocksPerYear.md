## Function: updateBlocksPerYear

### 📍 **Location & Path**
- **File**: `src/interest/JumpRateModelV4.sol`
- **Contract**: JumpRateModelV4
- **Line Numbers**: Lines 115 - 117
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/interest/JumpRateModelV4.sol/updateBlocksPerYear()`

### 📋 **Function Signature**
```solidity
function updateBlocksPerYear(uint256 blocksPerYear_) external onlyOwner {
    blocksPerYear = blocksPerYear_;
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: None
- **External Calls**: None
- **State Changes**: Updates `blocksPerYear`
- **Events Emitted**: None
- **Modifiers Applied**: `onlyOwner`

### 📖 **Function Summary**
**What it does:** I found this owner-only function sets the `blocksPerYear` constant used to derive per-block rates.
**Input Parameters:**
- `blocksPerYear_`: New estimated blocks per year
**Return Values:** None
**Side Effects:** Alters scaling for per-block rate calculations

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** The docs specify this simplifies interest calculations by allowing updates to assumed block rate. See `docs/src/src/interest/JumpRateModelV4.sol/contract.JumpRateModelV4.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: Medium
- **Financial Impact**: Yes (affects rate scaling)
- **External Dependencies**: None
- **Admin Privileges Required**: Yes

---
