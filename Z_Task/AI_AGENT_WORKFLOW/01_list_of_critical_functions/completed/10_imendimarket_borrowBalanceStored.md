## Function: IMendiMarket.borrowBalanceStored

### 📍 **Location & Path**
- **File**: `src/migration/IMigrator.sol`
- **Contract**: Interface IMendiMarket
- **Line Numbers**: Line 28
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/migration/IMigrator.sol/borrowBalanceStored()`

### 📋 **Function Signature**
```solidity
function borrowBalanceStored(address sender) external view returns (uint256);
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: N/A (interface)
- **External Calls**: Used by `_collectMendiPositions` to determine debts to refinance
- **State Changes**: None (view)
- **Events Emitted**: N/A
- **Modifiers Applied**: N/A

### 📖 **Function Summary**
**What it does:** I found this call reads the borrower’s outstanding principal used to size v2 borrow.
**Input Parameters:** `sender`
**Return Values:** Borrow balance
**Side Effects:** None

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Input to v2 borrowing amount selection. See `docs/src/src/migration/Migrator.sol/contract.Migrator.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: Medium
- **Financial Impact**: Indirect
- **External Dependencies**: Accurate accounting on v1
- **Admin Privileges Required**: No

---
