## Function: _updateJumpRateModel

### 📍 **Location & Path**
- **File**: `src/interest/JumpRateModelV4.sol`
- **Contract**: JumpRateModelV4
- **Line Numbers**: Lines 175 - 187
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/interest/JumpRateModelV4.sol/_updateJumpRateModel()`

### 📋 **Function Signature**
```solidity
function _updateJumpRateModel(
    uint256 baseRatePerYear,
    uint256 multiplierPerYear,
    uint256 jumpMultiplierPerYear,
    uint256 kink_
) private {
    baseRatePerBlock = baseRatePerYear / blocksPerYear;
    multiplierPerBlock = multiplierPerYear * 1e18 / (blocksPerYear * kink_);
    jumpMultiplierPerBlock = jumpMultiplierPerYear / blocksPerYear;
    kink = kink_;

    emit NewInterestParams(baseRatePerBlock, multiplierPerBlock, jumpMultiplierPerBlock, kink);
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: None
- **External Calls**: None
- **State Changes**: Updates `baseRatePerBlock`, `multiplierPerBlock`, `jumpMultiplierPerBlock`, `kink`
- **Events Emitted**: `NewInterestParams(...)`
- **Modifiers Applied**: None (private)

### 📖 **Function Summary**
**What it does:** I found this function derives per-block interest parameters from annualized inputs and sets the model state.
**Input Parameters:** Annualized rates and `kink_`
**Return Values:** None
**Side Effects:** Emits event and mutates model parameters

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Described as internal parameter updater invoked by owner calls and constructor. See `docs/src/src/interest/JumpRateModelV4.sol/contract.JumpRateModelV4.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High (math determines entire interest curve; division by zero risk if `blocksPerYear` or `kink_` are zero)
- **Financial Impact**: Yes
- **External Dependencies**: None
- **Admin Privileges Required**: Indirect (reachable via owner-only external functions)

---
