## Function: _checkSender

### 📍 **Location & Path**
- **File**: `src/mToken/extension/mTokenGateway.sol`
- **Contract**: mTokenGateway
- **Line Numbers**: Lines 339 - 348
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/extension/mTokenGateway.sol/_checkSender()`

### 📋 **Function Signature**
```solidity
function _checkSender(address msgSender, address srcSender) private view {
    if (msgSender != srcSender) {
        require(
            allowedCallers[srcSender][msgSender] || msgSender == owner()
                || _isAllowedFor(msgSender, _getProofForwarderRole())
                || _isAllowedFor(msgSender, _getBatchProofForwarderRole()),
            mTokenGateway_CallerNotAllowed()
        );
    }
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: `_isAllowedFor`, `_getProofForwarderRole`, `_getBatchProofForwarderRole`, `owner()`
- **External Calls**: None
- **State Changes**: None (view)
- **Events Emitted**: None
- **Modifiers Applied**: None

### 📖 **Function Summary**
**What it does:** I found this enforces that either the source user, their delegated caller, the owner, or privileged proof-forwarders can trigger releases.
**Input Parameters:** `msgSender`, `srcSender`
**Return Values:** None
**Side Effects:** None

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Authorization gate for release path. See `docs/src/src/mToken/extension/mTokenGateway.sol/contract.mTokenGateway.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High (authorization correctness)
- **Financial Impact**: Yes
- **External Dependencies**: Role config, per-user allowed callers
- **Admin Privileges Required**: Indirect

---
