## Function: _collectMendiPositions

### 📍 **Location & Path**
- **File**: `src/migration/Migrator.sol`
- **Contract**: Migrator
- **Line Numbers**: Lines 158 - 188
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/migration/Migrator.sol/_collectMendiPositions()`

### 📋 **Function Signature**
```solidity
function _collectMendiPositions(address user) private returns (Position[] memory) {
    IMendiMarket[] memory mendiMarkets = IMendiComptroller(MENDI_COMPTROLLER).getAssetsIn(user);
    uint256 marketsLength = mendiMarkets.length;

    Position[] memory positions = new Position[](marketsLength);
    uint256 positionCount;

    for (uint256 i = 0; i < marketsLength; i++) {
        IMendiMarket mendiMarket = mendiMarkets[i];
        uint256 collateralUnderlyingAmount = mendiMarket.balanceOfUnderlying(user);
        uint256 borrowAmount = mendiMarket.borrowBalanceStored(user);

        if (collateralUnderlyingAmount > 0 || borrowAmount > 0) {
            address maldaMarket = _getMaldaMarket(IMendiMarket(address(mendiMarket)).underlying());
            if (maldaMarket != address(0)) {
                positions[positionCount++] = Position({
                    mendiMarket: address(mendiMarket),
                    maldaMarket: maldaMarket,
                    collateralUnderlyingAmount: collateralUnderlyingAmount,
                    borrowAmount: borrowAmount
                });
            }
        }
    }

    // Resize array to actual position count
    assembly {
        mstore(positions, positionCount)
    }
    return positions;
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: `_getMaldaMarket(underlying)`
- **External Calls**: `IMendiComptroller.getAssetsIn(user)`, `IMendiMarket.balanceOfUnderlying(user)`, `IMendiMarket.borrowBalanceStored(user)`, `IMendiMarket.underlying()`
- **State Changes**: None in storage; constructs memory array via inline assembly resize
- **Events Emitted**: None
- **Modifiers Applied**: None

### 📖 **Function Summary**
**What it does:** I found this function builds a list of user positions worth migrating by querying Mendi and mapping to allowed Malda markets.
**Input Parameters:** `user`
**Return Values:** Dynamic `Position[]` adjusted to actual count
**Side Effects:** None on storage

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Gathers collateral/borrow data and filters by allowed target markets. See `docs/src/src/migration/Migrator.sol/contract.Migrator.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High (determines what will be migrated; relies on accurate market mapping)
- **Financial Impact**: Yes (drives downstream actions)
- **External Dependencies**: Mendi interfaces
- **Admin Privileges Required**: No

---
