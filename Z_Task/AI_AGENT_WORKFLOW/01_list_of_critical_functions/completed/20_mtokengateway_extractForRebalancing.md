## Function: extractForRebalancing

### 📍 **Location & Path**
- **File**: `src/mToken/extension/mTokenGateway.sol`
- **Contract**: mTokenGateway
- **Line Numbers**: Lines 176 - 179
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/extension/mTokenGateway.sol/extractForRebalancing()`

### 📋 **Function Signature**
```solidity
function extractForRebalancing(uint256 amount) external notPaused(OperationType.Rebalancing) {
    if (!rolesOperator.isAllowedFor(msg.sender, rolesOperator.REBALANCER())) revert mTokenGateway_NotRebalancer();
    IERC20(underlying).safeTransfer(msg.sender, amount);
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: None
- **External Calls**: `rolesOperator.isAllowedFor(...)`, `IERC20(underlying).safeTransfer(...)`
- **State Changes**: Token transfer out of contract
- **Events Emitted**: None
- **Modifiers Applied**: `notPaused(OperationType.Rebalancing)`

### 📖 **Function Summary**
**What it does:** I found this withdraws underlying to the rebalancer when rebalancing is enabled.
**Input Parameters:** `amount`
**Return Values:** None
**Side Effects:** Moves funds

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:** Rebalancer-controlled extraction for treasury/liquidity operations. See `docs/src/src/mToken/extension/mTokenGateway.sol/contract.mTokenGateway.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High (can move funds out)
- **Financial Impact**: Yes
- **External Dependencies**: IRoles, ERC20 implementation
- **Admin Privileges Required**: Role-based (REBALANCER)

---
