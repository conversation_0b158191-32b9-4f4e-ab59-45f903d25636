## Function: repayExternal

### 📍 **Location & Path**
- **File**: `src/mToken/host/mErc20Host.sol`
- **Contract**: mErc20Host
- **Line Numbers**: Lines 255 - 277
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/host/mErc20Host.sol/repayExternal()`

### 📋 **Function Signature**
```solidity
function repayExternal(
    bytes calldata journalData,
    bytes calldata seal,
    uint256[] calldata repayAmount,
    address receiver
) external override {
    if (!_isAllowedFor(msg.sender, _getBatchProofForwarderRole())) {
        _verifyProof(journalData, seal);
    }

    _checkOutflow(CommonLib.computeSum(repayAmount));

    bytes[] memory journals = _decodeJournals(journalData);
    uint256 length = journals.length;
    CommonLib.checkLengthMatch(length, repayAmount.length);

    for (uint256 i; i < length;) {
        _repayExternal(journals[i], repayAmount[i], receiver);
        unchecked {
            ++i;
        }
    }
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: `_verifyProof`, `_checkOutflow`, `_decodeJournals`, `_repayExternal`
- **External Calls**: None here
- **State Changes**: None here
- **Events Emitted**: None here
- **Modifiers Applied**: None

### 📖 **Function Summary**
Proof-gated cross-chain repay execution; enforces outflow limit and uses internal accounting.

### 📚 **Expected Behavior (Per Docs)**
See `docs/src/src/mToken/host/mErc20Host.sol/contract.mErc20Host.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High
- **Financial Impact**: Yes
- **External Dependencies**: IZkVerifier, Operator defender
- **Admin Privileges Required**: Role-based bypass for proof

---
