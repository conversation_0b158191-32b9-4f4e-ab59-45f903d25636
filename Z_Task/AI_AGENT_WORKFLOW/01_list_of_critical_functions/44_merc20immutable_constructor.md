## Function: constructor

### 📍 **Location & Path**
- **File**: `src/mToken/mErc20Immutable.sol`
- **Contract**: mErc20Immutable
- **Line Numbers**: Lines 40 - 59
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/mErc20Immutable.sol/constructor()`

### 📋 **Function Signature**
```solidity
constructor(
    address underlying_,
    address operator_,
    address interestRateModel_,
    uint256 initialExchangeRateMantissa_,
    string memory name_,
    string memory symbol_,
    uint8 decimals_,
    address payable admin_
) {
    admin = payable(msg.sender);

    // Initialize the market
    _initializeMErc20(
        underlying_, operator_, interestRateModel_, initialExchangeRateMantissa_, name_, symbol_, decimals_
    );

    // Set the proper admin now that initialization is done
    admin = admin_;
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: `_initializeMErc20(...)`
- **External Calls**: None
- **State Changes**: Initializes market and sets `admin`
- **Events Emitted**: None
- **Modifiers Applied**: None

### 📖 **Function Summary**
Constructs an immutable mToken market; incorrect parameters or admin assignment breaks core lending invariants and admin control.

### 📚 **Expected Behavior (Per Docs)**
See `docs/src/src/mToken/mErc20Immutable.sol/contract.mErc20Immutable.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High
- **Financial Impact**: Yes
- **External Dependencies**: Interest model
- **Admin Privileges Required**: N/A (constructor)

---
