## Function: performExtensionCall

### 📍 **Location & Path**
- **File**: `src/mToken/host/mErc20Host.sol`
- **Contract**: mErc20Host
- **Line Numbers**: Lines 282 - 300
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/host/mErc20Host.sol/performExtensionCall()`

### 📋 **Function Signature**
```solidity
function performExtensionCall(uint256 actionType, uint256 amount, uint32 dstChainId) external payable override {
    //actionType:
    // 1 - withdraw
    // 2 - borrow
    CommonLib.checkHostToExtension(amount, dstChainId, msg.value, allowedChains, gasHelper);
    _checkOutflow(amount);

    uint256 _amount = amount;
    if (actionType == 1) {
        _amount = _redeem(msg.sender, amount, false);
        emit mErc20Host_WithdrawOnExtensionChain(msg.sender, dstChainId, _amount);
    } else if (actionType == 2) {
        _borrow(msg.sender, amount, false);
        emit mErc20Host_BorrowOnExtensionChain(msg.sender, dstChainId, _amount);
    } else {
        revert mErc20Host_ActionNotAvailable();
    }
    acc[dstChainId].outPerChain[msg.sender] += _amount;
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: `_checkOutflow`, `_redeem`, `_borrow`
- **External Calls**: `CommonLib.checkHostToExtension(...)`
- **State Changes**: Updates `acc[dstChainId].outPerChain[msg.sender]`; mToken state via `_redeem`/`_borrow`
- **Events Emitted**: `mErc20Host_WithdrawOnExtensionChain`, `mErc20Host_BorrowOnExtensionChain`
- **Modifiers Applied**: None

### 📖 **Function Summary**
Cross-chain operation initiator for withdraw or borrow on extension chain; incorrect checks can violate outflow limits or chain allowlist.

### 📚 **Expected Behavior (Per Docs)**
See `docs/src/src/mToken/host/mErc20Host.sol/contract.mErc20Host.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High
- **Financial Impact**: Yes
- **External Dependencies**: Allowed chains, gas helper, operator defender via `_checkOutflow`
- **Admin Privileges Required**: No (user-initiated)

---
