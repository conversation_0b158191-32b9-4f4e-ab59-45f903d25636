## Function: _checkProofCall

### 📍 **Location & Path**
- **File**: `src/mToken/host/mErc20Host.sol`
- **Contract**: mErc20Host
- **Line Numbers**: Lines 335 - 340
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/host/mErc20Host.sol/_checkProofCall()`

### 📋 **Function Signature**
```solidity
function _checkProofCall(uint32 dstChainId, uint32 chainId, address market, address sender) internal view {
    _checkSender(msg.sender, sender);
    require(dstChainId == uint32(block.chainid), mErc20Host_DstChainNotValid());
    require(market == address(this), mErc20Host_AddressNotValid());
    require(allowedChains[chainId], mErc20Host_ChainNotValid());
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: `_checkSender`
- **External Calls**: None
- **State Changes**: None (view)
- **Events Emitted**: None
- **Modifiers Applied**: None

### 📖 **Function Summary**
Validates sender authorization, destination chain, market address, and allowed source chain before executing cross-chain actions.

### 📚 **Expected Behavior (Per Docs)**
See `docs/src/src/mToken/host/mErc20Host.sol/contract.mErc20Host.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High (bypass breaks cross-chain safety)
- **Financial Impact**: Yes
- **External Dependencies**: Allowed chains config
- **Admin Privileges Required**: Indirect

---
