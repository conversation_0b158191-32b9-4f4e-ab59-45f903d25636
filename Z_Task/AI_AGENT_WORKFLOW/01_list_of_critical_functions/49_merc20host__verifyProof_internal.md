## Function: _verifyProof

### 📍 **Location & Path**
- **File**: `src/mToken/host/mErc20Host.sol`
- **Contract**: mErc20Host
- **Line Numbers**: Lines 378 - 399
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/host/mErc20Host.sol/_verifyProof()`

### 📋 **Function Signature**
```solidity
function _verifyProof(bytes calldata journalData, bytes calldata seal) internal view {
    require(journalData.length > 0, mErc20Host_JournalNotValid());

    // Decode the dynamic array of journals.
    bytes[] memory journals = _decodeJournals(journalData);

    // Check the L1Inclusion flag for each journal.
    bool isSequencer = _isAllowedFor(msg.sender, _getProofForwarderRole())
        || _isAllowedFor(msg.sender, _getBatchProofForwarderRole());

    if (!isSequencer) {
        for (uint256 i = 0; i < journals.length; i++) {
            (,,,,,, bool L1Inclusion) = mTokenProofDecoderLib.decodeJournal(journals[i]);
            if (!L1Inclusion) {
                revert mErc20Host_L1InclusionRequired();
            }
        }
    }

    // verify it using the IZkVerifier contract
    verifier.verifyInput(journalData, seal);
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: `_decodeJournals`, `_isAllowedFor`, role getters
- **External Calls**: `verifier.verifyInput`
- **State Changes**: None (view)
- **Events Emitted**: None
- **Modifiers Applied**: None

### 📖 **Function Summary**
Performs L1 inclusion checks and zk-proof verification; bypass enables unauthorized cross-chain actions.

### 📚 **Expected Behavior (Per Docs)**
See `docs/src/src/mToken/host/mErc20Host.sol/contract.mErc20Host.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High
- **Financial Impact**: Yes
- **External Dependencies**: IZkVerifier, decoder lib, role config
- **Admin Privileges Required**: Indirect

---
