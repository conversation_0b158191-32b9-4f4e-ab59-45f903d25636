## Function: _checkSender

### 📍 **Location & Path**
- **File**: `src/mToken/host/mErc20Host.sol`
- **Contract**: mErc20Host
- **Line Numbers**: Lines 342 - 351
- **Full Path**: `/home/<USER>/MYSPACE/WEB3/BUGBOUNTY/Malda/2025-07-malda-maigadohcrypto/malda-lending/src/mToken/host/mErc20Host.sol/_checkSender()`

### 📋 **Function Signature**
```solidity
function _checkSender(address msgSender, address srcSender) internal view {
    if (msgSender != srcSender) {
        require(
            allowedCallers[srcSender][msgSender] || msgSender == admin
                || _isAllowedFor(msgSender, _getProofForwarderRole())
                || _isAllowedFor(msgSender, _getBatchProofForwarderRole()),
            mErc20Host_CallerNotAllowed()
        );
    }
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: `_isAllowedFor`, role getters
- **External Calls**: None
- **State Changes**: None (view)
- **Events Emitted**: None
- **Modifiers Applied**: None

### 📖 **Function Summary**
Authorization gate for actions affecting user balances; wrong logic enables unauthorized third-party execution.

### 📚 **Expected Behavior (Per Docs)**
See `docs/src/src/mToken/host/mErc20Host.sol/contract.mErc20Host.md`.

### ⚠️ **Critical Notes**
- **Risk Level**: High
- **Financial Impact**: Yes
- **External Dependencies**: Allowed callers mapping, role config
- **Admin Privileges Required**: Indirect

---
