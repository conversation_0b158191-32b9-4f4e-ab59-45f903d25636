### Summary
`utilizationRate` divides by `(cash + borrows - reserves)` without guarding for zero/underflow. When `reserves >= cash + borrows`, the subtraction underflows or becomes zero and reverts. This bubbles via `getBorrowRate` into market accrual, causing a market DoS of interest accrual and dependent flows.

### ### Root Cause
     
In `JumpRateModelV4.utilizationRate()`, the function computes utilization without checking if denominator could be zero/negative, here the link https://github.com/sherlock-audit/2025-07-malda-maigadohcrypto/blob/51f18a652acec56f71627ab8212e463c0aab2b41/malda-lending/src/interest/JumpRateModelV4.sol#L130 :
```solidity
function utilizationRate(uint256 cash, uint256 borrows, uint256 reserves) public pure override returns (uint256) {
    if (borrows == 0) {
        return 0;
    }
    return borrows * 1e18 / (cash + borrows - reserves); // vulnerable division without guard : line 134
}
```
Accrual calls `getBorrowRate` which calls `utilizationRate`, propagating the revert through the entire interest system.

Result: When `reserves >= cash + borrows` (achievable through liquidations increasing reserves and redemptions draining cash), any accrual or rate computation reverts, bricking the market.

### #PoC
Create a directory called vulnerabilities(or any name of your choice) in test folder, which is like this test/vulnerabilities, then cd, and create the test file UtilizationDenominatorZero.t.sol, then paste the below file and run the test:
```solidity
// SPDX-License-Identifier: BSL-1.1
pragma solidity =0.8.28;

import {Test} from "forge-std/Test.sol";
import {JumpRateModelV4} from "src/interest/JumpRateModelV4.sol";

contract UtilizationDenominatorZero is Test {
    function test_utilizationRate_RevertsWhenReservesExceedCashPlusBorrows() external {
        // Deploy a model with arbitrary parameters; specific values are irrelevant for this check
        JumpRateModelV4 model = new JumpRateModelV4({
            blocksPerYear_: 2_628_000,
            baseRatePerYear: 0,
            multiplierPerYear: 0,
            jumpMultiplierPerYear: 0,
            kink_: 1e18,
            owner_: address(this),
            name_: "TestModel"
        });

        // reserves > cash + borrows triggers (cash + borrows - reserves) underflow/zero
        uint256 cash = 0;
        uint256 borrows = 1;
        uint256 reserves = 2;

        vm.expectRevert();
        model.utilizationRate(cash, borrows, reserves);
    }

    function test_getBorrowRate_RevertsViaUtilizationRateDenominator() external {
        JumpRateModelV4 model = new JumpRateModelV4({
            blocksPerYear_: 2_628_000,
            baseRatePerYear: 1e17,
            multiplierPerYear: 0,
            jumpMultiplierPerYear: 0,
            kink_: 1e18,
            owner_: address(this),
            name_: "TestModel"
        });

        // Same state causes getBorrowRate() to revert through utilizationRate()
        vm.expectRevert();
        model.getBorrowRate({cash: 0, borrows: 1, reserves: 2});
    }
}
```

### Attack Path 
1. As liquidations execute over time, the protocol’s reserve balance grows from the seized‑collateral share added to `totalReserves` .
2. Redemptions withdraw cash from the market, reducing `totalUnderlying` while reserves remain.
3. With small outstanding `borrows`, the denominator `cash + borrows - reserves` eventually reaches zero or goes negative.
4. Any operation that triggers `accrueInterest()` calls `getBorrowRate()` → `utilizationRate()` and reverts with arithmetic underflow. The market becomes unusable until state is corrected.

The DoS persists until market state is manually restored via external reserves addition or other administrative intervention.

### Impact
High-severity DoS: interest accrual halts completely; borrowing/lending operations that depend on accrual become non-functional.


### Recommendation

- Add safe guard in `utilizationRate`:
```solidity
function utilizationRate(uint256 cash, uint256 borrows, uint256 reserves) public pure override returns (uint256) {
    if (borrows == 0) {
        return 0;
    }
    uint256 totalAssets = cash + borrows;
    if (totalAssets <= reserves) {
        return 0; // or return minimal safe value
    }
    return borrows * 1e18 / (totalAssets - reserves);
}
```

This prevents the underflow without changing economic behavior or parameters.


### Notes 
 Not privileged-only: achievable through normal market operations (liquidations + redemptions).
 


