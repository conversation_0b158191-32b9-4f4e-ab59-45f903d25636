### Summary
`Migrator.migrateAllPositions()` computes `minCollateral` in underlying units and passes it to `mErc20Host.mintOrBorrowMigration()` where it is interpreted as a minimum number of minted mTokens. For markets with `exchangeRate > ~1.111…`, the mint always reverts (`mt_MinAmountNotValid()`), causing a deterministic DoS of the migration flow.

### Root Cause
     
- In `Migrator.migrateAllPositions()`, the `minCollateral` is computed in underlying units and passed to mint flow expecting token units, here is the link https://github.com/sherlock-audit/2025-07-malda-maigadohcrypto/blob/51f18a652acec56f71627ab8212e463c0aab2b41/malda-lending/src/migration/Migrator.sol#L94 :
```solidity
// malda-lending/src/migration/Migrator.sol  line 94
uint256 minCollateral =
    position.collateralUnderlyingAmount - (position.collateralUnderlyingAmount * 1e4 / 1e5); // 90% underlying : line 95

ImErc20Host(position.maldaMarket).mintOrBorrowMigration(
    true, position.collateralUnderlyingAmount, msg.sender, address(0), minCollateral // underlying units passed as minAmount : line 97
);
```
line https://github.com/sherlock-audit/2025-07-malda-maigadohcrypto/blob/51f18a652acec56f71627ab8212e463c0aab2b41/malda-lending/src/mToken/host/mErc20Host.sol#L312
```solidity
_mint(receiver, receiver, amount, minAmount, false); // minAmount treated as token units : line 312
```
line https://github.com/sherlock-audit/2025-07-malda-maigadohcrypto/blob/51f18a652acec56f71627ab8212e463c0aab2b41/malda-lending/src/mToken/mToken.sol#L700
```solidity

require(mintTokens >= minAmountOut, mt_MinAmountNotValid()); // unit mismatch check fails : line 700
```

Result: With `minCollateral` in underlying units, check compares `mintTokens = underlying/exchangeRate` against `minCollateral`, failing when `exchangeRate > 1/0.9 ≈ 1.111…`.

### #PoC
Create a directory called vulnerabilities(or any name of your choice) in test folder, which is like this test/vulnerabilities, then cd, and create the test file Migrator_MinOutUnits_DoS.t.sol, then paste the below file and run the test:
```solidity
// SPDX-License-Identifier: BSL-1.1
pragma solidity =0.8.28;

// contracts
import {mErc20Host} from "src/mToken/host/mErc20Host.sol";

// tests shared setup
import {mToken_Unit_Shared} from "test/unit/shared/mToken_Unit_Shared.t.sol";
import {ERC1967Proxy} from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";

contract Migrator_MinOutUnits_DoS is mToken_Unit_Shared {
    mErc20Host public market;

    function setUp() public override {
        super.setUp();

        // Deploy host via proxy and initialize with exchangeRate = 2e18
        mErc20Host implementation = new mErc20Host();
        bytes memory initData = abi.encodeWithSelector(
            mErc20Host.initialize.selector,
            address(weth),
            address(operator),
            address(interestModel),
            2e18, // initialExchangeRateMantissa: exchange rate = 2
            "Test Market",
            "tmTEST",
            18,
            payable(address(this)),
            address(zkVerifier),
            address(roles)
        );
        ERC1967Proxy proxy = new ERC1967Proxy(address(implementation), initData);
        market = mErc20Host(address(proxy));

        // List market so Operator hooks allow minting
        operator.supportMarket(address(market));

        // Allow this contract to act as migrator
        market.setMigrator(address(this));
    }

    function test_Migration_Mints_Revert_On_Underlying_Denominated_MinOut() public {
        uint256 amountUnderlying = 100 ether;
        uint256 minCollateralUnderlying = (amountUnderlying * 90) / 100; // ~90% underlying, as in Migrator

        // With exchangeRate = 2, minted tokens = amount / 2 = 50e18 < 90e18 -> revert on mt_MinAmountNotValid
        vm.expectRevert();
        market.mintOrBorrowMigration(true, amountUnderlying, address(this), address(0), minCollateralUnderlying);
    }
}
```

### Attack Path (based on execution flow)
1. A call to `Migrator.migrateAllPositions()` on a market with `exchangeRate > 1.111`.
2. Migrator computes `minCollateral = collateralUnderlyingAmount * 90%` in underlying units.
3. Calls `mErc20Host.mintOrBorrowMigration(true, amount, receiver, 0, minCollateral)` with underlying-denominated `minCollateral`.
4. `mErc20Host._mint → mToken.__mint` compares `mintTokens = underlying/exchangeRate` against `minCollateral` as token units, causing unit mismatch revert. Migration halts; debts/positions remain unmigrated.

Migration fails deterministically on mature markets, causing persistent DoS consistent with the unit mismatch.

### Impact
- DoS of migration once `exchangeRate > 1/0.9 ≈ 1.111…` (normal condition as markets accrue interest).
- Users cannot complete position migration; v1 debts/collateral cannot be transitioned to v2, blocking intended fund movement until code is fixed.

### Recommendation

- Convert underlying `minCollateral` to token units in `migrateAllPositions`: compute `minTokensOut = minCollateral * 1e18 / exchangeRateStored()` and pass as `minAmount`; leave the host interface unchanged.

This aligns migrator semantics with documented "token units" interface without changing any external behavior or parameters.


### Notes 
- `setMigrator` is `onlyAdmin`; access control is correct but irrelevant to the unit mismatch.


