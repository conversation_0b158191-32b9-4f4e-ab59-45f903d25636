# Sherlock Audit Contest - Criteria for Issue Validity

Source: [<PERSON> Judging Guidelines](https://docs.sherlock.xyz/audits/judging/guidelines)

This guide aims to provide clarity for both Watsons & <PERSON> on various categories of issues that are valid under <PERSON>'s judging. <PERSON> is exclusively focused on high and medium-severity findings, as these vulnerabilities will cause a loss of user funds and most materially damage the reputation of the protocols Sherlock seeks to protect.

## I. Table of Contents:

* II. Criteria for Issue Severity:
* III. <PERSON>'s standards:
* IV. How to identify a high issue:
* V. How to identify a medium issue:
* VI. Recommendations:
* VII. List of Issue categories that are not considered valid:
* VIII. List of Issue categories that are considered valid:
* IX. Duplication guidelines:
* X. Best practices:

## II. Criteria for Issue Severity:

(repealed; see IV. How to identify a high issue: and V. How to identify a medium issue: for details)

## III. <PERSON>'s standards:

1. **Subjectivity:** Despite these guidelines, we must understand that because of the complexity & subjective nature of smart contract security, there may be issues that are judged beyond the purview of this guide. However, for the vast majority of cases, this guide should suffice. <PERSON>'s chosen judges continue to have the last word on considering any issue as valid or not.

2. **Hierarchy of truth:** If the protocol team provides no specific information, the default guidelines always apply.  
If the protocol team includes specific information in the README or CODE COMMENTS, that information may be used as context during the judging process. In cases where there is a contradiction between the README and CODE COMMENTS, the README should be considered the primary source of truth.  
The judge can decide that CODE COMMENTS are outdated. In that case, the default guidelines apply.  
> Example: The code comments state that "a swap can never fail" even though the code is built to support failing swaps.  
The protocol team can use the README (and only the README) to define the protocol's invariants/properties. Specifically, only the following question can be used for that:  
> What properties/invariants do you want to hold even if breaking them has a low/unknown impact?  
Issues that break the invariants from the above question, irrespective of whether the impact is low/unknown, could be assigned Medium severity if it doesn't conflict with common sense. High severity will be applied only if the issue falls into the High severity category in the judging guidelines.  
> Example: The README states "Admin can only call XYZ function once" but the code allows the Admin to call XYZ function twice; this is a valid Medium  
> Example: The README states "Variable X must always match USDC amount in the contract" and a user can donate USDC to break that invariant without causing issues to the protocol; this is an invalid issue  
The Sherlock Judge can use public statements up until 24h before the contest ends to override the language in the chosen source of truth.  
If guidelines are updated, the new guidelines apply only to contests that start after the date of change. Please check Criteria Changelog for information on the latest changes in the judging guidelines.  
**Historical decisions are not considered sources of truth.**

3. **Could Denial-of-Service (DOS), griefing, or locking of contracts count as Medium (or High) severity issue?** To judge the severity we use two separate criteria:  
   1. The issue causes funds to be locked for more than a week.  
   2. The issue impacts the availability of time-sensitive functions (cutoff functions are not considered time-sensitive).  
If at least one of these is describing the case, the issue can be Medium. If both apply, the issue can be considered High severity. Additional constraints related to the issue may decrease its severity accordingly.  
If a single occurrence of the attack results in a denial of service (DOS) for less than a week, the issue should be evaluated based on a **single occurrence** of the attack, even if it can be repeated indefinitely. It qualifies as a medium-level issue only if it disrupts a clearly time-sensitive function.  
> Note: if the single occurrence of the attack is relatively long (e.g. >2 days), and it takes only 2-3 iterations to cause a 7-day DOS, it may be considered a valid finding.

4. **(External) Admin trust assumptions**: If a protocol defines restrictions on the owner/admin, issues involving attacks that bypass these restrictions may be considered valid. These restrictions must be explicitly stated and will be assessed case by case. Admin functions are generally assumed to be used correctly and not harm users/the protocol.  
Note: if the (external) admin will unknowingly cause issues, it can be considered a valid issue. Note: the internal protocol roles are trusted by default. They can be considered untrusted (i.e. act maliciously) only if it's, specifically, claimed to be untrusted in the contest README OR the user can get the role without admin/owner permission (e.g. paying a specific fee).  
> Example: Admin sets fee to 200%. The issue "Admin can break deposit by setting fee to a 100%+" is invalid as it's common sense that fees can not be more than 100% on a deposit.  
> Example: Admin sets fee to 20%. This will cause liquidations to fail in case the utilization ratio is below 10%, this can be Medium as the admin is not aware of the consequences of his action.

5. **Contract Scope:**  
   1. If a contract is in contest Scope, then all its parent contracts are included by default.  
   2. In case the vulnerability exists in a library and an in-scope contract uses it and is affected by this bug this is a valid issue.  
   3. If there is a vulnerability in a contract from the contest repository but is not included in the scope then issues related to it cannot be considered valid.

6. **Design decisions** are not valid issues. Even if the design is suboptimal, but doesn't imply any loss of funds, these issues are considered informational.

## IV. How to identify a high issue:

1. Direct loss of funds without (extensive) limitations of external conditions. The loss of the affected party must be significant.

**Guidelines for Significant Loss:**

* Users lose more than 1% _and_ more than $10 of their principal.
* Users lose more than 1% _and_ more than $10 of their yield.
* The protocol loses more than 1% _and_ more than $10 of the fees.

## V. How to identify a medium issue:

1. Causes a loss of funds but requires certain external conditions or specific states, or a loss is highly constrained. The loss must be relevant to the affected party.
2. Breaks **core** contract functionality, rendering the contract useless or leading to loss of funds that's relevant to the affected party.

> Note: If a single attack can cause a 0.01% loss but can be replayed indefinitely, it will be considered a high-severity issue; however, if that attack would require external state change (admin action), it would be considered a medium-severity issue.

## VI. Recommendations:

Recommendations are not valid issues.

**Exceptions:**

Issues that are recommendations but show that other parts of the codebase may be problematic because of this problem can be considered valid.

**Examples:**

> Code lacks an important slippage check. There is no value directly lost, but it could cause harm and also has other negative effects. -> Invalid

> Code lacks an important slippage check. This will result in significant lost value. Here is one such example: users get 20% less value when this occurs in production. -> Valid

## VII. List of Issue categories that are not considered valid:

1. **Admin Input/call validation:** Issues related to admin inputting incorrect values or making incorrect external calls are not considered valid.
2. **Rounding/Precision issues:** Any rounding/precision issues that do not affect the normal operation of the code and do not lead to loss of funds are not considered valid.
3. **Reverting function calls** after they are not supposed to revert (soft DoS) are not considered a valid issue **unless** it's combined with another higher issue that is part of the submission or it blocks time-sensitive functionality.
4. **EIP Compliance:** For issues related to EIP compliance, the protocol & codebase must show that there are important external integrations that would require strong compliance with the EIP's implemented in the code. The EIP must be in regular use or in the **final state** for EIP implementation issues to be considered valid
5. **Issues related to using draft EIPs** are not valid.
6. **Function Calls or Transactions involving tokens with transfer fees** unless explicitly mentioned in the README. Even if transfer fee tokens were previously supported, without an explicit statement, they would not be considered valid by default.
7. **Issues in contracts with predetermined invariants. For example, Vault contracts. LP tokens are for predetermined times, yield farming contracts, etc.** In such contracts, it's acceptable to have functions that result in LP tokens to be worthless because the time has ended or these functions are being deprecated. The protocol is expected to be working on a newer version. It's not the protocol's responsibility to make sure LPs have something to redeem after predetermined time periods.
8. **Findings relying on external APIs such as those on Coingecko and CoinMarketCap** will not be considered valid.
9. **Slippage Protection when not explicitly required.** For example, if the trading function has a "minimum expected" argument that defaults to 0, and there is no check for slippage in the contract, it is understood that users are responsible for setting this value according to their requirements.
10. **Issues involving sandwich attacks that do not lead to value extraction** from the protocol or users are not valid. A sandwich attack is only valid if it results in a net loss for users.
11. **Loss of airdrops** or any other rewards that are not part of the original protocol design is not considered a valid high/medium. Example
12. **Use of Storage gaps:** Simple contracts with one of the parent contract not implementing storage gaps are considered low/informational.**Exception**: However, if the protocol design has a highly complex and branched set of contract inheritance with storage gaps inconsistently applied throughout and the submission clearly describes the necessity of storage gaps it can be considered a valid medium. Example
13. **Incorrect values in View functions** are by default considered **low**.**Exception**: In case any of these incorrect values returned by the view functions are used as a part of a larger function which would result in loss of funds then it would be a valid **medium/high** depending on the impact.
14. **Stale prices and Chainlink round completeness** Recommendations to implement round completeness or stale price checks for any oracle are invalid.  
> Exception: the recommendation to implement stale price checks **may** be valid. For example, the protocol may be using Pyth pull-based oracle, which requires requesting the price before using it. Hence, if we don't request the price firstly, or check it for staleness, then we can end up using very old price (e.g. from 1 hour/day ago).
15. Issues from the previous contests with `wont fix` labels (if it's an update contest) **and** issues from previous audits (linked in the contest README) marked as acknowledged (not fixed) are not considered valid.
16. **Chain re-org** and **network liveness** issues are not valid.
17. **ERC721 unsafe mint:** Issues where users cannot safemint ERC721 tokens due to unsupported implementation are not valid. Example: <https://github.com/sherlock-audit/2023-03-teller-judging/issues/8>
18. **Future issues:** Issues that result out of a future integration/implementation that was not mentioned in the docs/README or because of a future change in the code (as a fix to another issue) are **not** valid issues.
19. **Non-Standard tokens:** Issues related to tokens with non-standard behaviors, such as weird-tokens are not considered valid by default unless these tokens are explicitly mentioned in the README. Tokens with decimals between 6 and 18 are not considered weird.
20. Using Solidity versions that support **EVM opcodes that don't work** on networks on which the protocol is deployed is not a valid issue beacause one can manage compilation flags to compile for past EVM versions on newer Solidity versions.
21. **Sequencers** are assumed to operate reliably without misbehavior or downtime. Vulnerabilities or attacks that rely on sequencers going offline or malfunctioning are invalid.

## VIII. List of Issue categories that are considered valid:

1. **Slippage** related issues showing a direct loss of funds with a detailed explanation for the same can be considered valid **high**
2. **EIP Compliance:** For issues related to EIP compliance, the protocol & codebase must show that there are important external integrations that would require strong compliance with the EIP's implemented in the code. The EIP must be in regular use or in the **final state** for EIP implementation issues to be considered valid
3. **Identifies the core issue:** In case of issues that have a large number of duplicates, Issues that identify the core issue and show valid loss of funds should be grouped.
4. **Out of Gas:** Issues that result in Out of Gas errors either by the malicious user filling up the arrays or there is a practical call flow that results in OOG can be considered a valid **medium** or in cases of blocking all user funds forever maybe a valid **high**.**Exception:** In case the array length is controlled by the trusted admin/owner or the issue describes an impractical usage of parameters to reach OOG state then these submissions would be considered as **low**.
5. **Chainlink Price Checks**: Issues related to minAnswer and maxAnswer checks on Chainlink's Price Feeds are considered medium only if the Watson explicitly mentions the price feeds (e.g. USDC/ETH) for the in-scope tokens on the in-scope chains that require this check. **Additionally**, a proper attack path and at least medium severity impact must be included in the report. See this to know if min/maxAnswer are deprecated on the price feed.

## IX. Duplication guidelines:

The duplication guidelines assume we have a "target issue", and the "potential duplicate" of that issue needs to meet the following requirements to be considered a duplicate.

1. Identify the root cause
2. Identify at least a Medium impact
3. Identify a valid attack path or vulnerability path

Only when the "potential duplicate" meets all three requirements will the "potential duplicate" be duplicated with the "target issue", and all duplicates will be awarded the highest severity identified among the duplicates.

Otherwise, if the "potential duplicate" doesn't meet all requirements, the "potential duplicate" will not be duplicated but could still be judged any other way (solo, a duplicate of another issue, invalid, or any other severity)

**Root cause groupings**

If the following issues appear in multiple places, even in different contracts. In that case, they may be considered to have the same root cause.

1. Issues with the same logic mistake.  
> Example: uint256 is cast to uint128 unsafely.
2. Issues with the same conceptual mistake.  
> Example: different untrusted external admins can steal funds.
3. Issues in the category  
   * Slippage protection  
   * Reentrancy  
   * Access control  
   * Front-run / sandwich ( issue A that identifies a front-run and issue B that identifies a sandwich can be duplicated )

If the underlying code implementations, impact or fixes are different, then they may be treated separately.

For the root cause categories above, the duplication should be based on the following groups:

1. **Reentrancy:**  
   * Reenter in the same function;  
   * Cross function reentrancy (in a different function inside the contract);  
   * Cross contract reentrancy (in a different contract within the codebase);  
   * Read-only reentrancy.  
   * Cross-chain reentrancy.  
If several reports find different scenarios for the same type of reentrancy within the codebase, they should be considered to have the same root cause.

2. **Front-running/sandwich/slippage protection:**  
   * Can be fixed by slippage protection;  
   * Can be fixed by a commit-reveal mechanism (e.g. the user front-runs the admin, who's trying to blacklist them).

## X. Best practices:

1. Read the contest readme and documents thoroughly.
2. Submit issues that are considered valid according to Sherlock guidelines based on your discretion
3. Do not submit multiple issues in a single submission. Even if the 2 completely different issues occur on the same line, please make sure to separately submit them. Below is a valid example where multiple occurrences can be combined into one issue: Example. Also, there could be multiple occurrences of an issue but they may need to be submitted separately as each occurrence need not be an obvious repetition like the `safeTransfer` example. Watsons must use their own discretion in such cases.
4. Be specific and sufficiently descriptive when describing an issue's impact. Bad: Loss of funds for the user Good: Loss of funds for users as there is no access control for the 'withdraw' function
5. **Do not add** unnecessarily **long code snippets** into the submission. Keep the code snippet limited to the scope of the impact, and be as descriptive as possible in the Vulnerability Detail sections.
6. Do not copy-paste issues from other contests/reports/past audits. They are extremely unlikely to be valid to the respective contest.

This guide shall be regularly updated as more contests are judged and with evolving Smart contract security principles, the judging standards are subject to change over time.

## XI. Glossary

**Attack Path**: A sequence of steps or actions a malicious actor takes to cause losses or grief to the protocol or users and/or gain value or profit.

**Front-run**: There is operation A; if executed, it typically has no losses for anyone. If some other operation B executes before A, either in the same transaction or a separate transaction, there is a loss of some sort for either the protocol or some user(s) (it can be the same user)

**Root Cause**: The primary factor or a fundamental reason that imposes an unwanted outcome

**Sandwich**: A front-run, followed by operation C controlled by the attacker who also executed operation B. The goal of operation C is to revert the contract to its initial state.

**Vulnerability Path**: A sequence of steps showcasing how an issue causes losses or grief to the protocol or users through well-intended usage of the protocol.
