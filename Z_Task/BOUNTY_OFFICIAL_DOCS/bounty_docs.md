## Malda Protocol – Key Docs Extract Focused on This Codebase

Source: [Malda Docs](https://docs.malda.xyz/)

### Overview
Malda is a unified-liquidity lending protocol on Ethereum and multiple L2s. It delivers a seamless cross-chain lending experience via global liquidity pools, coordinated by a zk-powered coprocessor and verified on-chain. Over time, interest-rate and balance computations are moved off-chain into a zkVM, remaining fully verifiable while massively scaling throughput. The protocol vision: blockchains anchor verified state, while computation happens off-chain. See Malda docs overview for the narrative and scaling notes including the throughput intuition (Mainnet TPS → L2 TPS → ×100 via zkVM → Malda throughput) [Malda Docs](https://docs.malda.xyz/).

### Architecture (what matters to this repo)
- Global liquidity pools span multiple EVM chains and act in sync.
- A centralized sequencer orchestrates cross-chain operations but is constrained by zk proofs; users remain self-custodial and can self-sequence by generating their own proofs when needed.
- The zk coprocessor verifies cross-chain state and proves correctness of user operations. On-chain Solidity components enforce the verified results and keep canonical ledger state.

### End-user lifecycle (high-level)
1. User supplies/borrows in a market on any supported chain through Malda’s `mToken` markets.
2. Sequencer collects intents, builds/obtains zk proofs of cross-chain state, and submits operations.
3. On-chain contracts verify proofs, update balances, and enforce risk/interest logic across chains without the user bridging assets.

### Global Pools and Global Interest Rates
- Liquidity and interest rates are unified across supported chains to present a single market experience.
- Next-generation design moves interest-rate and balance calculations into a zkVM, enabling fully customizable interest curves with verifiable off-chain computation [Malda Docs](https://docs.malda.xyz/).

### Rebalancing
- Global pools require ongoing liquidity alignment across chains. A dedicated on-chain `Rebalancer` component coordinates with supported bridges to shift liquidity to where it’s needed, based on observed demand. The rebalancer is semi-trusted and designed to avoid custodial powers over user funds.

### Security Properties
- Self-custodial by design; sequencer cannot arbitrarily move user funds.
- zk proofs gate cross-chain operations and provide reorg protection.
- Admin/operational roles exist for pausing and safety, with clearly scoped powers (e.g., Pauser trusted; Sequencer and Rebalancer semi-trusted, focused on liveness and liquidity alignment).
- Users can self-sequence by generating and submitting their own proofs for censorship resistance.

### Oracles
- Price feeds combine trusted sources; the codebase includes `MixedPriceOracleV4` and `ChainlinkOracle` for robust pricing and gas-related helpers to control oracle costs.

---

## Mapping Docs Concepts to This Repository

This repo has two primary areas:
- `malda-lending` (Solidity smart contracts implementing on-chain protocol logic)
- `malda-zk-coprocessor` (Rust/RISC Zero components for zk proof generation/verification used by the protocol)

### malda-lending (core on-chain modules)
- mToken suite (markets and accounting)
  - `mToken.sol`, `mErc20.sol`, `mErc20Immutable.sol`, `mErc20Upgradable.sol`, `mTokenConfiguration.sol`, `mTokenStorage.sol`
  - `host/mErc20Host.sol`, `extension/mTokenGateway.sol` and `BatchSubmitter.sol` provide cross-chain entry, batching and host/extension patterns for global markets.
- Verifier
  - `verifier/ZkVerifier.sol` enforces zk proof verification on-chain to gate cross-chain operations consistent with the coprocessor.
- Oracles
  - `oracles/MixedPriceOracleV4.sol`, `oracles/ChainlinkOracle.sol`, and `oracles/gas/DefaultGasHelper.sol` provide pricing and gas-aware oracle utilities.
- Rebalancer and bridges
  - `rebalancer/Rebalancer.sol` plus bridge adapters like `bridges/AcrossBridge.sol` and `bridges/EverclearBridge.sol` move liquidity across chains for global pool health.
- Operational safety & roles
  - `pauser/Pauser.sol` for emergency halts; `Roles.sol` and `Operator` contracts for controlled administration.
- Rewards & referrals
  - `rewards/RewardDistributor.sol` and `referral/ReferralSigning.sol` enable incentives and referral mechanics.
- Migration utilities
  - `migration/Migrator.sol`, `IMigrator.sol` to assist moving positions/markets when needed.
- Utilities
  - `utils/LiquidationHelper.sol`, `utils/WrapAndSupply.sol`, and other libraries supporting market operations.

These components collectively implement the docs’ concepts of unified markets, proof-gated cross-chain actions, oracle-driven risk, and operational safety controls.

### malda-zk-coprocessor (zk proof layer)
- Generates zk proofs that validate cross-chain state and user operations across chains.
- Supports chain-specific verification (e.g., OP Stack commitments, L1 inclusion proofs) and reorg safety.
- Exposes program(s) used by the sequencer or by users for self-sequencing; on-chain verification integrates via `ZkVerifier.sol` in `malda-lending`.

---

## Additional Official Resources
- Documentation home: [Malda Docs](https://docs.malda.xyz/)
- Audit reports: [Audit Reports at Malda Docs](https://docs.malda.xyz/malda-protocol/audit-reports)

This file intentionally focuses on text content (no HTML) and mirrors the areas of the docs that define and contextualize the modules present in this codebase.

---

## Roles and Trust Model (from official guidance)
- Sequencer: centralized but constrained by zk proofs; executes cross-chain UserOps. Semi-trusted for liveness; cannot transfer user funds arbitrarily.
- Rebalancer: semi-trusted to shift liquidity; designed so it cannot move user funds, only influence availability (possible DoS via constant rebalancing).
- Pauser: trusted to pause markets for safety.
- Owner: trusted for configuration and upgrades per repository scope.

These constraints align with the protocol’s self-custodial design and zk verification guarantees described in the docs [Malda Docs](https://docs.malda.xyz/).

## Core Protocol Flows (mapped to modules)
- Supply/Redeem: via `mErc20` family and `host/extension` pattern; wraps native assets when needed (`WrapAndSupply.sol`).
- Borrow/Repay: enforced by `mToken` accounting and interest models; prices via oracles.
- Liquidation: facilitated by `LiquidationHelper.sol` and market configs; incentivizes third-party liquidators.
- Cross-chain entry: `mTokenGateway.sol` and `BatchSubmitter.sol` batch intents and coordinate with proofs.
- Proof verification: `ZkVerifier.sol` checks zk receipts before sensitive state changes.

## Cross-chain Architecture Pattern
- Host and Extension markets: `host/mErc20Host.sol` on a chain hosting liquidity; chain extensions interact through gateways and zk proofs, maintaining a unified market across chains.
- Bridges abstracted by the Rebalancer to reposition liquidity; user operations remain proof-gated and self-custodial.

## Oracles and Pricing
- Mixed-source pricing through `MixedPriceOracleV4.sol` with `ChainlinkOracle.sol` integration; gas-aware helpers via `oracles/gas/DefaultGasHelper.sol`.
- Robust pricing is critical for risk checks across unified markets.

## Rebalancing and Liquidity Alignment
- `Rebalancer.sol` plus bridges (e.g., `AcrossBridge.sol`, `EverclearBridge.sol`) maintain healthy liquidity distribution across chains based on demand.
- Designed with limited authority—cannot sweep user funds; aligns with semi-trusted role in the docs.

## Interest Rates
- Interest behavior evolves toward zkVM-based computation for customizable curves, with on-chain enforcement of results. In-code interest implementations (e.g., Jump Rate models) integrate with `mToken` to accrue interest per market.

## References
- Documentation home: [Malda Docs](https://docs.malda.xyz/)
- Audit reports index: [Audit Reports at Malda Docs](https://docs.malda.xyz/malda-protocol/audit-reports)


---

## Contract Addresses
- Addresses for core and market contracts are maintained in the official docs and deployments pages. They are chain-specific for networks such as Ethereum, Base, Linea, Optimism, Arbitrum, and Unichain. Typical components include: Deployer, Roles (RBAC), ZkVerifier, BatchSubmitter, GasHelper, RewardDistributor, Operator, Pauser, Oracles, and `mToken` markets. Always verify the current addresses in the docs before interacting on-chain [Malda Docs](https://docs.malda.xyz/).

## Operator
- The Operator component coordinates privileged operational flows consistent with the sequencer and zk verification model. It interacts with proof-gated entry points and enforces repository-scoped permissions and role constraints. Refer to the lending developer docs (Operator) for the responsibilities and callable methods in production environments [Malda Docs](https://docs.malda.xyz/).

## Global Accounts
- Users have a unified account experience across chains. State transitions are coordinated by the zk coprocessor and verified on-chain, so balances and positions behave as if they were on a single network even though operations occur on multiple chains. The on-chain contracts retain the canonical ledger state with proofs gating cross-chain updates [Malda Docs](https://docs.malda.xyz/).

## Use Cases
- Cross-chain supply/borrow without manual bridging or wrapping workflows.
- Unified interest rates and liquidity across multiple EVM chains.
- Capital-efficient liquidity rebalancing to meet demand where it arises.
- Self-sequencing for censorship resistance by generating and submitting your own proofs [Malda Docs](https://docs.malda.xyz/).

## Oracles (Detail)
- Price sources are designed for robustness and gas efficiency. Mixed-price aggregation and Chainlink integration are used to price collateral and borrows. Gas helpers help manage cross-chain oracle costs. See `MixedPriceOracleV4`, `ChainlinkOracle`, and `oracles/gas/DefaultGasHelper` in this repo and the oracles section in the docs for conceptual alignment [Malda Docs](https://docs.malda.xyz/).

## Development Setup
- ZK Coprocessor:
  - Prerequisites: Rust toolchain and RISC Zero toolchain installed.
  - Build: `cargo build`
  - Test: `cargo test`
  - Configure environment variables for supported chains and (optionally) proving backends as described in the docs [Malda Docs](https://docs.malda.xyz/).
- Lending Contracts:
  - Standard Solidity workflow (e.g., Foundry) for build/test/deploy.
  - Review module READMEs in `malda-lending/docs` for component-level references and the lending developer docs for integration notes [Malda Docs](https://docs.malda.xyz/).

## Testnet Guide and Faucets
- The docs include a testnet guide describing how to connect, interact with test deployments, and validate flows using zk proofs. Use official faucet links to obtain test assets for supported networks. Verify testnet addresses and endpoints in the docs before running integration tests or demos [Malda Docs](https://docs.malda.xyz/).

## Malda Points (Season 1 – The Rising)
- Points recognize participation and contributions within the ecosystem during designated seasons. The docs outline season objectives, how to earn points, and any associated terms or milestones. Refer to the current season page for the most up-to-date details [Malda Docs](https://docs.malda.xyz/).


