// SPDX-License-Identifier: BSL-1.1
pragma solidity =0.8.28;

// contracts
import {mErc20Host} from "src/mToken/host/mErc20Host.sol";

// tests shared setup
import {mToken_Unit_Shared} from "test/unit/shared/mToken_Unit_Shared.t.sol";
import {ERC1967Proxy} from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";

contract Migrator_MinOutUnits_DoS is mToken_Unit_Shared {
    mErc20Host public market;

    function setUp() public override {
        super.setUp();

        // Deploy host via proxy and initialize with exchangeRate = 2e18
        mErc20Host implementation = new mErc20Host();
        bytes memory initData = abi.encodeWithSelector(
            mErc20Host.initialize.selector,
            address(weth),
            address(operator),
            address(interestModel),
            2e18, // initialExchangeRateMantissa: exchange rate = 2
            "Test Market",
            "tmTEST",
            18,
            payable(address(this)),
            address(zkVerifier),
            address(roles)
        );
        ERC1967Proxy proxy = new ERC1967Proxy(address(implementation), initData);
        market = mErc20Host(address(proxy));

        // List market so Operator hooks allow minting
        operator.supportMarket(address(market));
        // Allow this contract to act as migrator
        market.setMigrator(address(this));
    }

    function test_Migration_Mints_Revert_On_Underlying_Denominated_MinOut() public {
        uint256 amountUnderlying = 100 ether;
        uint256 minCollateralUnderlying = (amountUnderlying * 90) / 100; // ~90% underlying, as in Migrator

        // With exchangeRate = 2, minted tokens = amount / 2 = 50e18 < 90e18 -> revert on mt_MinAmountNotValid
        vm.expectRevert();
        market.mintOrBorrowMigration(true, amountUnderlying, address(this), address(0), minCollateralUnderlying);
    }
}


