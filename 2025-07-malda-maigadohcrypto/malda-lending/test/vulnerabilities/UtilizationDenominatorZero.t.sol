// SPDX-License-Identifier: BSL-1.1
pragma solidity =0.8.28;

import {Test} from "forge-std/Test.sol";
import {JumpRateModelV4} from "src/interest/JumpRateModelV4.sol";

contract UtilizationDenominatorZero is Test {
    function test_utilizationRate_RevertsWhenReservesExceedCashPlusBorrows() external {
        // Deploy a model with arbitrary parameters; specific values are irrelevant for this check
        JumpRateModelV4 model = new JumpRateModelV4({
            blocksPerYear_: 2_628_000,
            baseRatePerYear: 0,
            multiplierPerYear: 0,
            jumpMultiplierPerYear: 0,
            kink_: 1e18,
            owner_: address(this),
            name_: "TestModel"
        });

        // reserves > cash + borrows triggers (cash + borrows - reserves) underflow/zero
        uint256 cash = 0;
        uint256 borrows = 1;
        uint256 reserves = 2;

        vm.expectRevert();
        model.utilizationRate(cash, borrows, reserves);
    }

    function test_getBorrowRate_RevertsViaUtilizationRateDenominator() external {
        JumpRateModelV4 model = new JumpRateModelV4({
            blocksPerYear_: 2_628_000,
            baseRatePerYear: 1e17,
            multiplierPerYear: 0,
            jumpMultiplierPerYear: 0,
            kink_: 1e18,
            owner_: address(this),
            name_: "TestModel"
        });

        // Same state causes getBorrowRate() to revert through utilizationRate()
        vm.expectRevert();
        model.getBorrowRate({cash: 0, borrows: 1, reserves: 2});
    }
}


