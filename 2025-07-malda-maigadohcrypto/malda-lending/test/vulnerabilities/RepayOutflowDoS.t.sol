// SPDX-License-Identifier: BSL-1.1
pragma solidity =0.8.28;

// interfaces
import {ImErc20Host} from "src/interfaces/ImErc20Host.sol";
import {IOperator} from "src/interfaces/IOperator.sol";
import {OperatorStorage} from "src/Operator/OperatorStorage.sol";

// tests shared setup
import {mToken_Unit_Shared} from "../unit/shared/mToken_Unit_Shared.t.sol";

contract RepayOutflowDoS is mToken_Unit_Shared {
    function setUp() public override {
        super.setUp();

        // allow current chain for cross-chain journal processing
        mWethHost.updateAllowedChain(uint32(block.chainid), true);

        // list market and set oracle prices (production-like path, no custom mocks besides in-repo ones)
        operator.supportMarket(address(mWethHost));
        oracleOperator.setPrice(DEFAULT_ORACLE_PRICE);
        oracleOperator.setUnderlyingPrice(DEFAULT_ORACLE_PRICE);
        // enable borrowing by setting collateral factor
        operator.setCollateralFactor(address(mWethHost), DEFAULT_COLLATERAL_FACTOR);
    }

    function test_RepayExternalConsumesOutflow_ThenBlocksOutflowOps() external {
        // Configure outflow limit to equal the USD value of the repay amount,
        // so the first repay fills the bucket exactly and the next outflow reverts.
        uint256 repayAmount = 10 ether;
        // amountInUSD = (amount * oraclePrice) / 1e18 / 1e10; with DEFAULT_ORACLE_PRICE = 1e18 → amount / 1e10
        uint256 limitUSD = (repayAmount * DEFAULT_ORACLE_PRICE) / 1e18 / 1e10;
        operator.setOutflowTimeLimitInUSD(limitUSD);

        // Prepare a real borrow so that repayment is valid and doesn't underflow
        // Supply > repay and then borrow exactly repayAmount on the same host market
        _repayPrerequisites(address(mWethHost), repayAmount * 2, repayAmount);

        // Create a valid single-entry journal matching current chain and market
        uint256[] memory repayAmounts = new uint256[](1);
        repayAmounts[0] = repayAmount;
        bytes memory repayJournal = _createAccumulatedAmountJournal(address(this), address(mWethHost), repayAmount);

        // Baselines
        uint256 cumBefore = operator.cumulativeOutflowVolume();
        uint256 userUnderlyingBefore = weth.balanceOf(address(this));

        // Act: call repayExternal via host (zk path uses in-repo verifier mock configured in shared setup)
        mWethHost.repayExternal(repayJournal, hex"", repayAmounts, address(this));

        // Assert: no user token movement on host-repay; but outflow counter increased to the limit
        assertEq(weth.balanceOf(address(this)), userUnderlyingBefore, "repayExternal should not transfer underlying");
        assertGt(operator.cumulativeOutflowVolume(), cumBefore, "outflow counter must increase");
        assertEq(operator.cumulativeOutflowVolume(), limitUSD, "repay should consume exactly the configured limit");

        // Any subsequent outflow-checked op should revert with OutflowVolumeReached.
        // Use mintExternal with an amount that yields amountInUSD >= 1 and a journal
        // whose accAmount is large enough to satisfy operation checks.
        uint256[] memory mintAmounts = new uint256[](1);
        uint256[] memory minOut = new uint256[](1);
        mintAmounts[0] = 1e10; // USD = 1 with price=1e18; ensures positive outflow
        minOut[0] = 0;
        bytes memory mintJournal = _createAccumulatedAmountJournal(
            address(this),
            address(mWethHost),
            repayAmount + mintAmounts[0] // ensure _accAmountIn - acc.inPerChain > mintAmount
        );

        vm.expectRevert(OperatorStorage.Operator_OutflowVolumeReached.selector);
        mWethHost.mintExternal(mintJournal, hex"", mintAmounts, minOut, address(this));
    }
}


