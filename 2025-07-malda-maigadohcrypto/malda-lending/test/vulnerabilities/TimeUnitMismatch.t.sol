// SPDX-License-Identifier: BSL-1.1
pragma solidity =0.8.28;

// contracts
import {JumpRateModelV4} from "src/interest/JumpRateModelV4.sol";
import {mErc20Immutable} from "src/mToken/mErc20Immutable.sol";

// tests shared setup
import {mToken_Unit_Shared} from "../unit/shared/mToken_Unit_Shared.t.sol";

contract TimeUnitMismatch is mToken_Unit_Shared {
    uint256 constant SECONDS_PER_YEAR = 365 days; // 31,536,000

    function test_TimeUnitMismatch_AccrualOvercharges() external {
        // Configure interest model with realistic blocks/year and base rate
        // Choose 2_628_000 blocks/year (~12 sec per block on L1)
        uint256 blocksPerYear = 2_628_000;
        uint256 baseRatePerYear = 10e16; // 10% APR scaled by 1e18
        uint256 multiplierPerYear = 0; // simplify to isolate base rate
        uint256 jumpMultiplierPerYear = 0;
        uint256 kinkMantissa = 1e18; // 100%

        JumpRateModelV4 customModel = new JumpRateModelV4(
            blocksPerYear,
            baseRatePerYear,
            multiplierPerYear,
            jumpMultiplierPerYear,
            kinkMantissa,
            address(this),
            "TestModel"
        );

        // Deploy a market with this model
        mErc20Immutable market = new mErc20Immutable(
            address(weth),
            address(operator),
            address(customModel),
            1e18,
            "Test Market",
            "tmWETH",
            18,
            payable(address(this))
        );

        // List market and set collateral factor (requires non-zero oracle price)
        operator.supportMarket(address(market));
        oracleOperator.setPrice(DEFAULT_ORACLE_PRICE);
        oracleOperator.setUnderlyingPrice(DEFAULT_ORACLE_PRICE);
        operator.setCollateralFactor(address(market), 8e17); // 80%

        // Supply and borrow to create non-zero borrows
        uint256 supplyAmount = 1_000 ether;
        uint256 borrowAmount = 100 ether;

        // Mint
        _getTokens(weth, address(this), supplyAmount);
        weth.approve(address(market), supplyAmount);
        market.mint(supplyAmount, address(this), supplyAmount);

        // Borrow
        market.borrow(borrowAmount);

        // Snapshot before accrual
        uint256 borrowsBefore = market.totalBorrows();

        // Advance time by 1 hour
        uint256 dt = 3600; // seconds
        vm.warp(block.timestamp + dt);

        // Accrue interest (uses timestamp seconds)
        market.accrueInterest();

        uint256 borrowsAfter = market.totalBorrows();
        uint256 actualInterest = borrowsAfter - borrowsBefore;

        // 1) The protocol's actual interest should match the per-block formula multiplied by seconds
        //    (i.e., expectedPerBlockInterest) within a tiny rounding margin
        {
            uint256 baseRatePerBlock = baseRatePerYear / blocksPerYear;
            uint256 expectedPerBlockInterest = (borrowsBefore * baseRatePerBlock * dt) / 1e18;
            if (actualInterest > expectedPerBlockInterest) {
                assertLe(actualInterest - expectedPerBlockInterest, 1);
            } else {
                assertLe(expectedPerBlockInterest - actualInterest, 1);
            }
        }

        // 2) And it should significantly exceed the correct per-second interest (~secondsPerYear/blocksPerYear)
        {
            // Guard against division by zero
            uint256 expectedPerSecondInterest = (borrowsBefore * ((baseRatePerYear / SECONDS_PER_YEAR)) * dt) / 1e18;
            require(expectedPerSecondInterest > 0, "expected per-second interest is zero");

            uint256 ratioTimes1e18 = (actualInterest * 1e18) / expectedPerSecondInterest; // ~ secondsPerYear / blocksPerYear
            uint256 expectedRatioTimes1e18 = (SECONDS_PER_YEAR * 1e18) / blocksPerYear; // ~ 12e18
            uint256 lower = (expectedRatioTimes1e18 * 95) / 100; // -5%
            uint256 upper = (expectedRatioTimes1e18 * 105) / 100; // +5%
            assertGe(ratioTimes1e18, lower);
            assertLe(ratioTimes1e18, upper);
        }
    }
}


