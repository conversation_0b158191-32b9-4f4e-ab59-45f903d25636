{"generic": {"version": 1, "connextDomains": [{"chainId": 1, "domainId": 6648936}, {"chainId": 42161, "domainId": 1634886255}, {"chainId": 10, "domainId": 10}, {"chainId": 8453, "domainId": 1650553709}]}, "networks": {"linea": {"ownership": "******************************************", "chainId": 59144, "isHost": true, "deployer": {"owner": "******************************************", "salt": "DeployerV1"}, "oracle": {"oracleType": "MixedPriceOracleV3", "stalenessPeriod": 86400}, "markets": [{"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 6, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": "mUSDC Interest Model"}, "name": "mUSDC", "supplyCap": 0, "symbol": "mUSDC", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 18, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": "mWETH Interest Model"}, "name": "mWETH", "supplyCap": 0, "symbol": "mWETH", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 6, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": "mUSDT Interest Model"}, "name": "mUSDT", "supplyCap": 0, "symbol": "mUSDT", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 18, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": "mDAI Interest Model"}, "name": "mDAI", "supplyCap": 0, "symbol": "mDAI", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 6, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": "mWBTC Interest Model"}, "name": "mWBTC", "supplyCap": 0, "symbol": "mWBTC", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 18, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": "mwstETH Interest Model"}, "name": "mwstETH", "supplyCap": 0, "symbol": "mwstETH", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 18, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": "mezETH Interest Model"}, "name": "mezETH", "supplyCap": 0, "symbol": "mezETH", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 18, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": "mweETH Interest Model"}, "name": "mweETH", "supplyCap": 0, "symbol": "mweETH", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 18, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": "mwrsETH Interest Model"}, "name": "mwrsETH", "supplyCap": 0, "symbol": "mwrsETH", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}], "roles": [{"accounts": ["******************************************"], "roleName": "CHAINS_MANAGER"}, {"accounts": ["******************************************"], "roleName": "PAUSE_MANAGER"}, {"accounts": ["******************************************"], "roleName": "PROOF_FORWARDER"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_RESERVE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_PAUSE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_BORROW_CAP"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_SUPPLY_CAP"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_ORACLE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_BRIDGE"}, {"accounts": ["******************************************"], "roleName": "REBALANCER_EOA"}], "zkVerifier": {"imageId": "0xd1333b14cc29a5d8828e9f5842a44c881efbabec67faf033633816939bde3c08", "verifierAddress": "0x8EaB2D97Dfce405A1692a21b3ff3A172d593D319"}, "allowedChains": [42161, 10, 8453]}, "mainnet": {"ownership": "******************************************", "chainId": 1, "isHost": false, "deployer": {"owner": "******************************************", "salt": "DeployerV1"}, "markets": [{"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 0, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mUSDC", "supplyCap": 0, "symbol": "mUSDC", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 0, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mWETH", "supplyCap": 0, "symbol": "mWETH", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 0, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mUSDT", "supplyCap": 0, "symbol": "mUSDT", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 0, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mDAI", "supplyCap": 0, "symbol": "mDAI", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 0, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mWBTC", "supplyCap": 0, "symbol": "mWBTC", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 0, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mwstETH", "supplyCap": 0, "symbol": "mwstETH", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 0, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mezETH", "supplyCap": 0, "symbol": "mezETH", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 0, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mweETH", "supplyCap": 0, "symbol": "mweETH", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 0, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mwrsETH", "supplyCap": 0, "symbol": "mwrsETH", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}], "roles": [{"accounts": ["******************************************"], "roleName": "CHAINS_MANAGER"}, {"accounts": ["******************************************"], "roleName": "PAUSE_MANAGER"}, {"accounts": ["******************************************"], "roleName": "PROOF_FORWARDER"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_RESERVE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_PAUSE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_BORROW_CAP"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_SUPPLY_CAP"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_ORACLE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_BRIDGE"}, {"accounts": ["******************************************"], "roleName": "REBALANCER_EOA"}], "zkVerifier": {"imageId": "0xd1333b14cc29a5d8828e9f5842a44c881efbabec67faf033633816939bde3c08", "verifierAddress": "0x8EaB2D97Dfce405A1692a21b3ff3A172d593D319"}}, "base": {"ownership": "******************************************", "chainId": 8453, "isHost": false, "deployer": {"owner": "******************************************", "salt": "DeployerV1"}, "markets": [{"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 0, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mUSDC", "supplyCap": 0, "symbol": "mUSDC", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 0, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mWETH", "supplyCap": 0, "symbol": "mWETH", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 0, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mUSDT", "supplyCap": 0, "symbol": "mUSDT", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 0, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mDAI", "supplyCap": 0, "symbol": "mDAI", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 0, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mWBTC", "supplyCap": 0, "symbol": "mWBTC", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 0, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mwstETH", "supplyCap": 0, "symbol": "mwstETH", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 0, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mezETH", "supplyCap": 0, "symbol": "mezETH", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 0, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mweETH", "supplyCap": 0, "symbol": "mweETH", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 0, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mwrsETH", "supplyCap": 0, "symbol": "mwrsETH", "underlying": "******************************************", "reserveFactor": 0, "liquidationBonus": 0}], "roles": [{"accounts": ["******************************************"], "roleName": "CHAINS_MANAGER"}, {"accounts": ["******************************************"], "roleName": "PAUSE_MANAGER"}, {"accounts": ["******************************************"], "roleName": "PROOF_FORWARDER"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_RESERVE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_PAUSE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_BORROW_CAP"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_SUPPLY_CAP"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_ORACLE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_BRIDGE"}, {"accounts": ["******************************************"], "roleName": "REBALANCER_EOA"}], "zkVerifier": {"imageId": "0xd1333b14cc29a5d8828e9f5842a44c881efbabec67faf033633816939bde3c08", "verifierAddress": "0x0b144E07A0826182B6b59788c34b32Bfa86Fb711"}}}}