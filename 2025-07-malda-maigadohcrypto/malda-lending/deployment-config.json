{"networks": {"linea_sepolia": {"chainId": 59141, "isHost": true, "deployer": {"owner": "******************************************", "salt": "FirstTry"}, "oracle": {"oracleType": "MixedPriceOracleV3", "stalenessPeriod": 86400, "usdcFeed": "******************************************", "wethFeed": "******************************************"}, "markets": [{"borrowCap": 1000000000000, "borrowRateMaxMantissa": 500000000000000000, "collateralFactor": *****************0, "decimals": 6, "interestModel": {"baseRate": 20000000000000000, "blocksPerYear": 2102400, "jumpMultiplier": 500000000000000000, "kink": *****************0, "multiplier": *****************0, "name": "USDC Interest Model"}, "name": "mUSDC", "priceFeed": "******************************************", "supplyCap": 1000000000000, "symbol": "mUSDC", "underlying": "******************************************"}, {"borrowCap": *****************00000, "borrowRateMaxMantissa": 500000000000000000, "collateralFactor": 750000000000000000, "decimals": 18, "interestModel": {"baseRate": *****************, "blocksPerYear": 2102400, "jumpMultiplier": 400000000000000000, "kink": 900000000000000000, "multiplier": *****************, "name": "WETH Interest Model"}, "name": "mWETH", "priceFeed": "******************************************", "supplyCap": *****************00000, "symbol": "mWETH", "underlying": "******************************************"}], "roles": [{"accounts": ["******************************************"], "roleName": "CHAINS_MANAGER"}, {"accounts": ["******************************************"], "roleName": "PAUSE_MANAGER"}, {"accounts": ["******************************************", "******************************************"], "roleName": "PROOF_FORWARDER"}, {"accounts": ["******************************************"], "roleName": "PROOF_BATCH_FORWARDER"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_RESERVE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_PAUSE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_BORROW_CAP"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_SUPPLY_CAP"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_ORACLE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_BRIDGE"}, {"accounts": ["******************************************"], "roleName": "REBALANCER_EOA"}, {"accounts": ["******************************************"], "roleName": "REBALANCER"}], "zkVerifier": {"imageId": "0xd1333b14cc29a5d8828e9f5842a44c881efbabec67faf033633816939bde3c08", "verifierAddress": "0x27983ee173aD10E171D17C9c5C14d5baFE997609"}, "allowedChains": [********, ********]}, "op_sepolia": {"chainId": ********, "isHost": false, "deployer": {"owner": "******************************************", "salt": "FirstTry"}, "markets": [{"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 6, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mUSDC", "priceFeed": "******************************************", "supplyCap": 0, "symbol": "mUSDC", "underlying": "******************************************"}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 18, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mWETH", "priceFeed": "******************************************", "supplyCap": 0, "symbol": "mWETH", "underlying": "******************************************"}], "roles": [{"accounts": ["******************************************"], "roleName": "CHAINS_MANAGER"}, {"accounts": ["******************************************"], "roleName": "PAUSE_MANAGER"}, {"accounts": ["******************************************", "******************************************"], "roleName": "PROOF_FORWARDER"}, {"accounts": ["******************************************", "******************************************"], "roleName": "PROOF_BATCH_FORWARDER"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_RESERVE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_PAUSE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_BORROW_CAP"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_SUPPLY_CAP"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_ORACLE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_BRIDGE"}, {"accounts": ["******************************************"], "roleName": "REBALANCER_EOA"}, {"accounts": ["******************************************"], "roleName": "REBALANCER"}], "zkVerifier": {"imageId": "0xd1333b14cc29a5d8828e9f5842a44c881efbabec67faf033633816939bde3c08", "verifierAddress": "0xB369b4dd27FBfb59921d3A4a3D23AC2fc32FB908"}}, "sepolia": {"chainId": ********, "isHost": false, "deployer": {"owner": "******************************************", "salt": "FirstTry"}, "markets": [{"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 6, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mUSDC", "priceFeed": "******************************************", "supplyCap": 0, "symbol": "mUSDC", "underlying": "******************************************"}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 18, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mWETH", "priceFeed": "******************************************", "supplyCap": 0, "symbol": "mWETH", "underlying": "******************************************"}], "roles": [{"accounts": ["******************************************"], "roleName": "CHAINS_MANAGER"}, {"accounts": ["******************************************"], "roleName": "PAUSE_MANAGER"}, {"accounts": ["******************************************", "******************************************"], "roleName": "PROOF_FORWARDER"}, {"accounts": ["******************************************", "******************************************"], "roleName": "PROOF_BATCH_FORWARDER"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_RESERVE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_PAUSE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_BORROW_CAP"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_SUPPLY_CAP"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_ORACLE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_BRIDGE"}, {"accounts": ["******************************************"], "roleName": "REBALANCER_EOA"}, {"accounts": ["******************************************"], "roleName": "REBALANCER"}], "zkVerifier": {"imageId": "0xd1333b14cc29a5d8828e9f5842a44c881efbabec67faf033633816939bde3c08", "verifierAddress": "0x925d8331ddc0a1F0d96E68CF073DFE1d92b69187"}}}}