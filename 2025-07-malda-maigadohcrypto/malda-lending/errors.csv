Contract,Error Name,Selector
interest\JumpRateModelV4.sol,JumpRateModelV4_MultiplierNotValid(),0xdda62321
interfaces\external\across\IAcrossSpokePoolV3.sol,DisabledRoute(),0x2a58c4f3
interfaces\external\across\IAcrossSpokePoolV3.sol,InvalidQuoteTimestamp(),0xf722177f
interfaces\external\across\IAcrossSpokePoolV3.sol,InvalidFillDeadline(),0x582e3889
interfaces\external\across\IAcrossSpokePoolV3.sol,InvalidExclusiveRelayer(),0x495d907f
interfaces\external\across\IAcrossSpokePoolV3.sol,MsgValueDoesNotMatchInputAmount(),0x6452a35d
interfaces\external\across\IAcrossSpokePoolV3.sol,NotExclusiveRelayer(),0xc3a9b9d0
interfaces\external\across\IAcrossSpokePoolV3.sol,NoSlowFillsInExclusivityWindow(),0x09deb9ec
interfaces\external\across\IAcrossSpokePoolV3.sol,RelayFilled(),0x8f260c60
interfaces\external\across\IAcrossSpokePoolV3.sol,InvalidSlowFillRequest(),0x012f9e44
interfaces\external\across\IAcrossSpokePoolV3.sol,ExpiredFillDeadline(),0xd642b7d9
interfaces\external\across\IAcrossSpokePoolV3.sol,InvalidMerkleProof(),0xb05e92fa
interfaces\external\across\IAcrossSpokePoolV3.sol,InvalidChainId(),0x7a47c9a2
interfaces\external\across\IAcrossSpokePoolV3.sol,InvalidMerkleLeaf(),0xcd298b38
interfaces\external\across\IAcrossSpokePoolV3.sol,ClaimedMerkleLeaf(),0x954476d9
interfaces\external\across\IAcrossSpokePoolV3.sol,InvalidPayoutAdjustmentPct(),0xa693f051
interfaces\external\across\IAcrossSpokePoolV3.sol,WrongERC7683OrderId(),0x1e191e8e
interfaces\external\across\IAcrossSpokePoolV3.sol,LowLevelCallFailed(bytesdata),0x5ab5dfae
interfaces\external\layerzero\v2\ILayerZeroOFT.sol,InvalidLocalDecimals(),0x1e9714b0
interfaces\external\layerzero\v2\ILayerZeroOFT.sol,SlippageExceeded(uint256amountLD,uint256minAmountLD),0x72d5a2f9
interfaces\ImErc20Host.sol,mErc20Host_ProofGenerationInputNotValid(),0x80e73bca
interfaces\ImErc20Host.sol,mErc20Host_DstChainNotValid(),0x2b25fce3
interfaces\ImErc20Host.sol,mErc20Host_ChainNotValid(),0x57e05e49
interfaces\ImErc20Host.sol,mErc20Host_AddressNotValid(),0xfb7dcd6b
interfaces\ImErc20Host.sol,mErc20Host_AmountTooBig(),0x61ee36eb
interfaces\ImErc20Host.sol,mErc20Host_AmountNotValid(),0xfd7850ff
interfaces\ImErc20Host.sol,mErc20Host_JournalNotValid(),0xc39550bb
interfaces\ImErc20Host.sol,mErc20Host_CallerNotAllowed(),0x271bd90b
interfaces\ImErc20Host.sol,mErc20Host_NotRebalancer(),0x00a12223
interfaces\ImErc20Host.sol,mErc20Host_LengthMismatch(),0x02e262f1
interfaces\ImErc20Host.sol,mErc20Host_NotEnoughGasFee(),0xc2bcba66
interfaces\ImErc20Host.sol,mErc20Host_L1InclusionRequired(),0x3f9e8a43
interfaces\ImTokenGateway.sol,mTokenGateway_ChainNotValid(),0xe125bbfd
interfaces\ImTokenGateway.sol,mTokenGateway_AddressNotValid(),0x1ab3c0c6
interfaces\ImTokenGateway.sol,mTokenGateway_AmountNotValid(),0x23366b73
interfaces\ImTokenGateway.sol,mTokenGateway_JournalNotValid(),0x357aa116
interfaces\ImTokenGateway.sol,mTokenGateway_AmountTooBig(),0x123b28f5
interfaces\ImTokenGateway.sol,mTokenGateway_ReleaseCashNotAvailable(),0x5c579b8b
interfaces\ImTokenGateway.sol,mTokenGateway_NonTransferable(),0x84aa360a
interfaces\ImTokenGateway.sol,mTokenGateway_CallerNotAllowed(),0x8531e415
interfaces\ImTokenGateway.sol,mTokenGateway_Paused(ImTokenOperationTypes.OperationType_type),0xba33a915
interfaces\ImTokenGateway.sol,mTokenGateway_NotRebalancer(),0x77392928
interfaces\ImTokenGateway.sol,mTokenGateway_LengthNotValid(),0x9a29b8a9
interfaces\ImTokenGateway.sol,mTokenGateway_NotEnoughGasFee(),0x1d919099
interfaces\ImTokenGateway.sol,mTokenGateway_L1InclusionRequired(),0x7a5eede8
interfaces\ImTokenGateway.sol,mTokenGateway_UserNotWhitelisted(),0xc29834e9
interfaces\IPauser.sol,Pauser_EntryNotFound(),0xd25c8ae9
interfaces\IPauser.sol,Pauser_NotAuthorized(),0xbde2b66f
interfaces\IPauser.sol,Pauser_AddressNotValid(),0xbd54cda9
interfaces\IPauser.sol,Pauser_AlreadyRegistered(),0x0550f760
interfaces\IPauser.sol,Pauser_ContractNotEnabled(),0xec3514d5
interfaces\IRebalancer.sol,Rebalancer_NotAuthorized(),0xbd5ce2cc
interfaces\IRebalancer.sol,Rebalancer_MarketNotValid(),0xce9c3a21
interfaces\IRebalancer.sol,Rebalancer_RequestNotValid(),0x7d6bb5ce
interfaces\IRebalancer.sol,Rebalancer_AddressNotValid(),0x45fc899c
interfaces\IRebalancer.sol,Rebalancer_BridgeNotWhitelisted(),0x2de9e220
interfaces\IRebalancer.sol,Rebalancer_TransferSizeExcedeed(),0xbce05243
interfaces\IRebalancer.sol,Rebalancer_TransferSizeMinNotMet(),0xebe13f6b
interfaces\IRebalancer.sol,Rebalancer_DestinationNotWhitelisted(),0xf5c1a6b7
interfaces\IRoles.sol,Roles_InputNotValid(),0xa9f5c73a
libraries\mTokenProofDecoderLib.sol,mTokenProofDecoderLib_ChainNotFound(),0x2258c6d4
libraries\mTokenProofDecoderLib.sol,mTokenProofDecoderLib_InvalidLength(),0xe12a4311
libraries\mTokenProofDecoderLib.sol,mTokenProofDecoderLib_InvalidInclusion(),0xa991c51f
libraries\SafeApprove.sol,SafeApprove_NoContract(),0x0695cf16
libraries\SafeApprove.sol,SafeApprove_Failed(),0xf388e876
mToken\BatchSubmitter.sol,BatchSubmitter_CallerNotAllowed(),0xf054412a
mToken\BatchSubmitter.sol,BatchSubmitter_JournalNotValid(),0xeccfb764
mToken\BatchSubmitter.sol,BatchSubmitter_InvalidSelector(),0xd645250f
mToken\BatchSubmitter.sol,BatchSubmitter_AddressNotValid(),0x542a98f1
mToken\mErc20.sol,mErc20_TokenNotValid(),0x9e149097
mToken\mTokenStorage.sol,mToken_OnlyAdmin(),0x303676d3
mToken\mTokenStorage.sol,mToken_RedeemEmpty(),0xbbf3e564
mToken\mTokenStorage.sol,mToken_InvalidInput(),0xbca3ab89
mToken\mTokenStorage.sol,mToken_OnlyAdminOrRole(),0x90b0f0f7
mToken\mTokenStorage.sol,mToken_TransferNotValid(),0x98a79b54
mToken\mTokenStorage.sol,mToken_MinAmountNotValid(),0xc8079268
mToken\mTokenStorage.sol,mToken_BorrowRateTooHigh(),0x8179ac1a
mToken\mTokenStorage.sol,mToken_AlreadyInitialized(),0xc99314d8
mToken\mTokenStorage.sol,mToken_ReserveFactorTooHigh(),0xea1eeb45
mToken\mTokenStorage.sol,mToken_ExchangeRateNotValid(),0xa08b42d0
mToken\mTokenStorage.sol,mToken_MarketMethodNotValid(),0x3e1fc327
mToken\mTokenStorage.sol,mToken_LiquidateSeizeTooMuch(),0x7286c375
mToken\mTokenStorage.sol,mToken_RedeemCashNotAvailable(),0x14ec588c
mToken\mTokenStorage.sol,mToken_BorrowCashNotAvailable(),0xcd85b93e
mToken\mTokenStorage.sol,mToken_ReserveCashNotAvailable(),0x72d33f5d
mToken\mTokenStorage.sol,mToken_RedeemTransferOutNotPossible(),0xc3a74f04
mToken\mTokenStorage.sol,mToken_SameChainOperationsAreDisabled(),0xd7ce5f3d
mToken\mTokenStorage.sol,mToken_CollateralBlockTimestampNotValid(),0xead6e3f4
nft\MaldaNft.sol,MaldaNft_MerkleRootNotSet(),0x473afab3
nft\MaldaNft.sol,MaldaNft_InvalidMerkleProof(),0x829e3c91
nft\MaldaNft.sol,MaldaNft_TokenAlreadyMinted(),0x4e903e00
nft\MaldaNft.sol,MaldaNft_TokenAlreadyClaimed(),0x2566fe00
nft\MaldaNft.sol,MaldaNft_TokenNotTransferable(),0x0e7e9c45
Operator\OperatorStorage.sol,Operator_Paused(),0x49fbea8b
Operator\OperatorStorage.sol,Operator_Mismatch(),0x80956214
Operator\OperatorStorage.sol,Operator_OnlyAdmin(),0x88965846
Operator\OperatorStorage.sol,Operator_EmptyPrice(),0x6eda0f7b
Operator\OperatorStorage.sol,Operator_WrongMarket(),0xc3b90d6a
Operator\OperatorStorage.sol,Operator_InvalidInput(),0xe5d0f96f
Operator\OperatorStorage.sol,Operator_AssetNotFound(),0xb8c54a64
Operator\OperatorStorage.sol,Operator_RepayingTooMuch(),0x9145f3dc
Operator\OperatorStorage.sol,Operator_OnlyAdminOrRole(),0xdfae8776
Operator\OperatorStorage.sol,Operator_MarketNotListed(),0x41dccaf4
Operator\OperatorStorage.sol,Operator_PriceFetchFailed(),0x7107178f
Operator\OperatorStorage.sol,Operator_SenderMustBeToken(),0xbb6425da
Operator\OperatorStorage.sol,Operator_UserNotWhitelisted(),0xefc6ae33
Operator\OperatorStorage.sol,Operator_MarketSupplyReached(),0x5b025ead
Operator\OperatorStorage.sol,Operator_RepayAmountNotValid(),0x9ad9ef8e
Operator\OperatorStorage.sol,Operator_MarketAlreadyListed(),0x00a52fd6
Operator\OperatorStorage.sol,Operator_OutflowVolumeReached(),0x7d8fb3e8
Operator\OperatorStorage.sol,Operator_InvalidRolesOperator(),0xef9b8572
Operator\OperatorStorage.sol,Operator_InsufficientLiquidity(),0x84c1d201
Operator\OperatorStorage.sol,Operator_MarketBorrowCapReached(),0xc4047e0f
Operator\OperatorStorage.sol,Operator_InvalidCollateralFactor(),0x04c8480e
Operator\OperatorStorage.sol,Operator_InvalidRewardDistributor(),0x89c9e960
Operator\OperatorStorage.sol,Operator_OracleUnderlyingFetchError(),0xd12b3c7a
Operator\OperatorStorage.sol,Operator_Deactivate_MarketBalanceOwed(),0xc75ffbd0
oracles\ChainlinkOracle.sol,ChainlinkOracle_NoPriceFeed(),0x60d219e5
oracles\ChainlinkOracle.sol,ChainlinkOracle_ZeroPrice(),0xa9276b5a
oracles\MixedPriceOracleV3.sol,MixedPriceOracle_Unauthorized(),0x07eb074d
oracles\MixedPriceOracleV3.sol,MixedPriceOracle_StalePrice(),0xa749793e
oracles\MixedPriceOracleV3.sol,MixedPriceOracle_InvalidPrice(),0x66ed0c58
oracles\MixedPriceOracleV3.sol,MixedPriceOracle_InvalidRound(),0x3af440e5
oracles\MixedPriceOracleV3.sol,MixedPriceOracle_InvalidConfig(),0x013a97c0
oracles\MixedPriceOracleV4.sol,MixedPriceOracle_Unauthorized(),0x07eb074d
oracles\MixedPriceOracleV4.sol,MixedPriceOracle_ApiV3StalePrice(),0x17d5b798
oracles\MixedPriceOracleV4.sol,MixedPriceOracle_eOracleStalePrice(),0xaf9a1420
oracles\MixedPriceOracleV4.sol,MixedPriceOracle_InvalidPrice(),0x66ed0c58
oracles\MixedPriceOracleV4.sol,MixedPriceOracle_InvalidRound(),0x3af440e5
oracles\MixedPriceOracleV4.sol,MixedPriceOracle_InvalidConfig(),0x013a97c0
oracles\MixedPriceOracleV4.sol,MixedPriceOracle_InvalidConfigDecimals(),0x90509023
oracles\MixedPriceOracleV4.sol,MixedPriceOracle_DeltaTooHigh(),0x26da6151
oracles\MixedPriceOracleV4.sol,MixedPriceOracle_MissingFeed(),0xa88a8ec3
rebalancer\bridges\AcrossBridge.sol,AcrossBridge_TokenMismatch(),0xd8319163
rebalancer\bridges\AcrossBridge.sol,AcrossBridge_NotAuthorized(),0x59e9d347
rebalancer\bridges\AcrossBridge.sol,AcrossBridge_NotImplemented(),0x7c1704dd
rebalancer\bridges\AcrossBridge.sol,AcrossBridge_AddressNotValid(),0x7339356e
rebalancer\bridges\AcrossBridge.sol,AcrossBridge_SlippageNotValid(),0xd2d96944
rebalancer\bridges\AcrossBridge.sol,AcrossBridge_RelayerNotValid(),0x5432a9eb
rebalancer\bridges\BaseBridge.sol,BaseBridge_NotAuthorized(),0x8e78fc1c
rebalancer\bridges\BaseBridge.sol,BaseBridge_AmountMismatch(),0xe80c7db9
rebalancer\bridges\BaseBridge.sol,BaseBridge_AmountNotValid(),0xeb3836b9
rebalancer\bridges\BaseBridge.sol,BaseBridge_AddressNotValid(),0xf993411a
rebalancer\bridges\ConnextBridge.sol,Connext_NotEnoughFees(),0xf9684068
rebalancer\bridges\ConnextBridge.sol,Connext_NotImplemented(),0x9328ca82
rebalancer\bridges\ConnextBridge.sol,Connext_DomainIdNotSet(),0x4afac92d
rebalancer\bridges\ConnextBridge.sol,Connext_DelegateNotValid(),0x1bc821bf
rebalancer\bridges\EverclearBridge.sol,Everclear_NotImplemented(),0x2749ea19
rebalancer\bridges\EverclearBridge.sol,Everclear_AddressNotValid(),0xfa31f4bf
rebalancer\bridges\LZBridge.sol,LZBridge_NotEnoughFees(),0xf7265d60
rebalancer\bridges\LZBridge.sol,LZBridge_ChainNotRegistered(),0xc4391226
rebalancer\bridges\LZBridge.sol,LZBridge_DestinationMismatch(),0x4aa12632
referral\ReferralSigning.sol,ReferralSigning_SameUser(),0x28691906
referral\ReferralSigning.sol,ReferralSigning_InvalidSignature(),0x6625befd
referral\ReferralSigning.sol,ReferralSigning_UserAlreadyReferred(),0xe5ad7885
referral\ReferralSigning.sol,ReferralSigning_ContractReferrerNotAllowed(),0xea07ce0f
rewards\RewardDistributor.sol,RewardDistributor_OnlyOperator(),0xbb83e1a4
rewards\RewardDistributor.sol,RewardDistributor_TransferFailed(),0x75b4a7d5
rewards\RewardDistributor.sol,RewardDistributor_RewardNotValid(),0xa44fd6c7
rewards\RewardDistributor.sol,RewardDistributor_AddressNotValid(),0x943c15c7
rewards\RewardDistributor.sol,RewardDistributor_AddressAlreadyRegistered(),0x7b113e09
rewards\RewardDistributor.sol,RewardDistributor_SupplySpeedArrayLengthMismatch(),0xdf20b370
rewards\RewardDistributor.sol,RewardDistributor_BorrowSpeedArrayLengthMismatch(),0x4ff13d70
utils\Deployer.sol,NotAuthorized(addressadmin,addresssender),0xfafe1054
utils\WrapAndSupply.sol,WrapAndSupply_AddressNotValid(),0x98409bd6
utils\WrapAndSupply.sol,WrapAndSupply_AmountNotValid(),0xea6c270b
verifier\ZkVerifier.sol,ZkVerifier_ImageNotValid(),0xfa79c3a0
verifier\ZkVerifier.sol,ZkVerifier_InputNotValid(),0x2756006b
verifier\ZkVerifier.sol,ZkVerifier_VerifierNotSet(),0xf242df53